"""
数据上报API
实现数据上报和文件上传功能
"""

import os
import base64
import logging
from typing import Dict, Any, Optional, Union, List

from .client import APIClient
from ..utils.validators import validate_required_fields
from ..exceptions.base import NifaValidationError
from ..auth.encryption import SM4Encryption

logger = logging.getLogger(__name__)


class DataAPI:
    """数据上报API类"""
    
    def __init__(self, client: Optional[APIClient] = None):
        """
        初始化数据上报API
        
        Args:
            client: API客户端实例，如果不提供则创建新实例
        """
        self.client = client or APIClient()
        self._own_client = client is None
    
    def upload_data(
        self,
        data_type: str,
        data_content: Union[str, Dict[str, Any], List[Dict[str, Any]]],
        batch_no: Optional[str] = None,
        encrypt: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        上报数据
        
        Args:
            data_type: 数据类型
            data_content: 数据内容
            batch_no: 批次号（可选）
            encrypt: 是否加密
            **kwargs: 其他参数
            
        Returns:
            上报结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not data_type or not data_type.strip():
            raise NifaValidationError("数据类型不能为空", field="data_type", value=data_type)
        
        if not data_content:
            raise NifaValidationError("数据内容不能为空", field="data_content", value=data_content)
        
        # 处理数据内容
        if isinstance(data_content, (dict, list)):
            import json
            content_str = json.dumps(data_content, ensure_ascii=False)
        else:
            content_str = str(data_content)
        
        # 加密处理
        if encrypt:
            try:
                from ..config.settings import settings
                if settings.NIFA_SM4_KEY:
                    sm4_encryption = SM4Encryption(settings.NIFA_SM4_KEY)
                    content_str = sm4_encryption.encrypt(content_str)
                else:
                    logger.warning("未配置SM4密钥，跳过加密")
            except Exception as e:
                logger.error(f"数据加密失败: {str(e)}")
                raise NifaValidationError(f"数据加密失败: {str(e)}")
        
        # 构建请求数据
        request_data = {
            "dataType": data_type,
            "dataContent": content_str,
            "encrypted": encrypt,
            **kwargs
        }
        
        if batch_no:
            request_data["batchNo"] = batch_no
        
        # 验证必填字段
        validate_required_fields(request_data, ["dataType", "dataContent"])
        
        logger.info(f"上报数据: 类型={data_type}, 加密={encrypt}")
        
        # 发起请求
        response = self.client.post("data/upload", data=request_data)
        
        return response
    
    def upload_file(
        self,
        file_path: str,
        file_type: str,
        description: Optional[str] = None,
        encrypt: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        上传文件
        
        Args:
            file_path: 文件路径
            file_type: 文件类型
            description: 文件描述
            encrypt: 是否加密
            **kwargs: 其他参数
            
        Returns:
            上传结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not file_path or not os.path.exists(file_path):
            raise NifaValidationError("文件路径不存在", field="file_path", value=file_path)
        
        if not file_type or not file_type.strip():
            raise NifaValidationError("文件类型不能为空", field="file_type", value=file_type)
        
        # 检查文件大小（假设限制为10MB）
        file_size = os.path.getsize(file_path)
        if file_size > 10 * 1024 * 1024:
            raise NifaValidationError(
                "文件大小不能超过10MB",
                field="file_size",
                value=file_size
            )
        
        # 读取文件内容
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except Exception as e:
            raise NifaValidationError(f"读取文件失败: {str(e)}")
        
        # Base64编码
        file_content_b64 = base64.b64encode(file_content).decode('utf-8')
        
        # 加密处理
        if encrypt:
            try:
                from ..config.settings import settings
                if settings.NIFA_SM4_KEY:
                    sm4_encryption = SM4Encryption(settings.NIFA_SM4_KEY)
                    file_content_b64 = sm4_encryption.encrypt(file_content_b64)
                else:
                    logger.warning("未配置SM4密钥，跳过加密")
            except Exception as e:
                logger.error(f"文件加密失败: {str(e)}")
                raise NifaValidationError(f"文件加密失败: {str(e)}")
        
        # 构建请求数据
        request_data = {
            "fileName": os.path.basename(file_path),
            "fileType": file_type,
            "fileContent": file_content_b64,
            "fileSize": file_size,
            "encrypted": encrypt,
            **kwargs
        }
        
        if description:
            request_data["description"] = description
        
        # 验证必填字段
        validate_required_fields(request_data, ["fileName", "fileType", "fileContent"])
        
        logger.info(f"上传文件: {os.path.basename(file_path)} ({file_size} bytes)")
        
        # 发起请求
        response = self.client.post("file/upload", data=request_data)
        
        return response
    
    def batch_upload_data(
        self,
        data_list: List[Dict[str, Any]],
        batch_no: Optional[str] = None,
        encrypt: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        批量上报数据
        
        Args:
            data_list: 数据列表
            batch_no: 批次号
            encrypt: 是否加密
            **kwargs: 其他参数
            
        Returns:
            上报结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not data_list or not isinstance(data_list, list):
            raise NifaValidationError("数据列表不能为空且必须为列表", field="data_list", value=data_list)
        
        if len(data_list) > 1000:  # 假设批量上报限制为1000条
            raise NifaValidationError(
                "批量上报数量不能超过1000条",
                field="data_list",
                value=len(data_list)
            )
        
        # 验证每个数据项
        for i, item in enumerate(data_list):
            if not isinstance(item, dict):
                raise NifaValidationError(
                    f"数据列表第{i+1}项必须为字典",
                    field=f"data_list[{i}]",
                    value=item
                )
            
            validate_required_fields(item, ["dataType", "dataContent"])
        
        # 处理数据内容
        processed_data_list = []
        for item in data_list:
            processed_item = item.copy()
            
            # 处理数据内容
            data_content = processed_item["dataContent"]
            if isinstance(data_content, (dict, list)):
                import json
                content_str = json.dumps(data_content, ensure_ascii=False)
            else:
                content_str = str(data_content)
            
            processed_item["dataContent"] = content_str
            processed_data_list.append(processed_item)
        
        # 加密处理
        if encrypt:
            try:
                from ..config.settings import settings
                if settings.NIFA_SM4_KEY:
                    sm4_encryption = SM4Encryption(settings.NIFA_SM4_KEY)
                    for item in processed_data_list:
                        item["dataContent"] = sm4_encryption.encrypt(item["dataContent"])
                else:
                    logger.warning("未配置SM4密钥，跳过加密")
            except Exception as e:
                logger.error(f"数据加密失败: {str(e)}")
                raise NifaValidationError(f"数据加密失败: {str(e)}")
        
        # 构建请求数据
        request_data = {
            "dataList": processed_data_list,
            "encrypted": encrypt,
            **kwargs
        }
        
        if batch_no:
            request_data["batchNo"] = batch_no
        
        logger.info(f"批量上报数据: {len(data_list)}条记录, 加密={encrypt}")
        
        # 发起请求
        response = self.client.post("data/batch-upload", data=request_data)
        
        return response
    
    def get_upload_status(
        self,
        task_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取上报状态
        
        Args:
            task_id: 任务ID
            **kwargs: 其他参数
            
        Returns:
            上报状态
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not task_id or not task_id.strip():
            raise NifaValidationError("任务ID不能为空", field="task_id", value=task_id)
        
        # 构建请求数据
        request_data = {
            "taskId": task_id,
            **kwargs
        }
        
        logger.info(f"获取上报状态: {task_id}")
        
        # 发起请求
        response = self.client.post("data/upload-status", data=request_data)
        
        return response
    
    def close(self) -> None:
        """关闭API客户端"""
        if self._own_client and self.client:
            self.client.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
