"""
API客户端测试
测试HTTP客户端的各种功能
"""

import pytest
import requests
from unittest.mock import Mock, patch, MagicMock
from requests.exceptions import RequestException, Timeout, ConnectionError

from nifa.api.client import APIClient
from nifa.exceptions.base import (
    NifaAPIError,
    NifaResponseError,
    NifaTimeoutError,
    NifaNetworkError
)


class TestAPIClient:
    """API客户端测试类"""
    
    def test_init_with_defaults(self):
        """测试使用默认参数初始化"""
        client = APIClient()
        assert client.base_url == "https://test-api.nifa.org.cn"  # 来自测试环境
        assert client.timeout == 30
        assert client.max_retries == 3
        assert client.retry_delay == 1.0
        assert client.session is not None
        assert client.signer is not None
    
    def test_init_with_custom_params(self):
        """测试使用自定义参数初始化"""
        client = APIClient(
            base_url="https://custom.api.com",
            org_code="CUSTOM123",
            timeout=60,
            max_retries=5,
            retry_delay=2.0
        )
        assert client.base_url == "https://custom.api.com"
        assert client.org_code == "CUSTOM123"
        assert client.timeout == 60
        assert client.max_retries == 5
        assert client.retry_delay == 2.0
    
    def test_create_session(self):
        """测试会话创建"""
        client = APIClient()
        session = client.session
        
        # 验证会话配置
        assert isinstance(session, requests.Session)
        assert len(session.adapters) >= 2  # http和https适配器
    
    def test_build_url(self):
        """测试URL构建"""
        client = APIClient(base_url="https://api.example.com")
        
        # 测试基础URL构建
        url = client._build_url("test/endpoint")
        assert url == "https://api.example.com/test/endpoint"
        
        # 测试带前导斜杠的端点
        url = client._build_url("/test/endpoint")
        assert url == "https://api.example.com/test/endpoint"
        
        # 测试空端点
        url = client._build_url("")
        assert url == "https://api.example.com/"
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_success(self, mock_request):
        """测试成功的请求"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": "0000",
            "message": "成功",
            "data": {"result": "test"}
        }
        mock_response.headers = {"Content-Type": "application/json"}
        mock_request.return_value = mock_response
        
        client = APIClient()
        result = client.request("GET", "test/endpoint")
        
        assert result["code"] == "0000"
        assert result["data"]["result"] == "test"
        mock_request.assert_called_once()
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_with_data(self, mock_request):
        """测试带数据的请求"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"code": "0000", "data": {}}
        mock_response.headers = {"Content-Type": "application/json"}
        mock_request.return_value = mock_response
        
        client = APIClient()
        test_data = {"key": "value"}
        client.request("POST", "test/endpoint", data=test_data)
        
        # 验证请求参数
        call_args = mock_request.call_args
        assert call_args[1]["json"] is not None  # 数据应该被包含
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_timeout(self, mock_request):
        """测试请求超时"""
        mock_request.side_effect = Timeout("Request timeout")
        
        client = APIClient()
        with pytest.raises(NifaTimeoutError):
            client.request("GET", "test/endpoint")
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_connection_error(self, mock_request):
        """测试连接错误"""
        mock_request.side_effect = ConnectionError("Connection failed")
        
        client = APIClient()
        with pytest.raises(NifaNetworkError):
            client.request("GET", "test/endpoint")
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_http_error(self, mock_request):
        """测试HTTP错误"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.return_value = {
            "code": "5000",
            "message": "服务器错误"
        }
        mock_response.headers = {"Content-Type": "application/json"}
        mock_request.return_value = mock_response
        
        client = APIClient()
        with pytest.raises(NifaAPIError):
            client.request("GET", "test/endpoint")
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_invalid_json(self, mock_request):
        """测试无效JSON响应"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = ValueError("Invalid JSON")
        mock_response.text = "Invalid response"
        mock_response.headers = {"Content-Type": "text/html"}
        mock_request.return_value = mock_response
        
        client = APIClient()
        with pytest.raises(NifaResponseError):
            client.request("GET", "test/endpoint")
    
    def test_get_method(self):
        """测试GET方法"""
        client = APIClient()
        with patch.object(client, 'request') as mock_request:
            mock_request.return_value = {"code": "0000"}
            
            params = {"param1": "value1"}
            client.get("test/endpoint", params=params)
            
            mock_request.assert_called_once_with(
                'GET', 'test/endpoint', params=params
            )
    
    def test_post_method(self):
        """测试POST方法"""
        client = APIClient()
        with patch.object(client, 'request') as mock_request:
            mock_request.return_value = {"code": "0000"}
            
            data = {"key": "value"}
            client.post("test/endpoint", data=data)
            
            mock_request.assert_called_once_with(
                'POST', 'test/endpoint', data=data
            )
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with APIClient() as client:
            assert isinstance(client, APIClient)
            assert client.session is not None
        
        # 验证会话已关闭
        assert client.session is None
    
    def test_close(self):
        """测试关闭客户端"""
        client = APIClient()
        session = client.session
        
        client.close()
        
        assert client.session is None
    
    @patch('nifa.api.client.requests.Session.request')
    def test_request_without_signing(self, mock_request):
        """测试不签名的请求"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"code": "0000", "data": {}}
        mock_response.headers = {"Content-Type": "application/json"}
        mock_request.return_value = mock_response
        
        client = APIClient()
        test_data = {"key": "value"}
        client.request("POST", "test/endpoint", data=test_data, sign_request=False)
        
        # 验证数据没有被签名处理
        call_args = mock_request.call_args
        assert call_args[1]["json"] == test_data
    
    @patch('nifa.api.client.requests.Session.request')
    def test_validate_response_structure(self, mock_request):
        """测试响应结构验证"""
        # 测试缺少必要字段的响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"invalid": "response"}
        mock_response.headers = {"Content-Type": "application/json"}
        mock_request.return_value = mock_response
        
        client = APIClient()
        # 这应该仍然返回响应，因为我们的验证比较宽松
        result = client.request("GET", "test/endpoint")
        assert result == {"invalid": "response"}
    
    def test_request_context_manager(self):
        """测试请求上下文管理器"""
        client = APIClient()
        
        # 测试正常情况下上下文管理器的行为
        with patch.object(client, '_request_context') as mock_context:
            mock_context.return_value.__enter__ = Mock()
            mock_context.return_value.__exit__ = Mock()
            
            with patch('nifa.api.client.requests.Session.request') as mock_request:
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {"code": "0000"}
                mock_response.headers = {"Content-Type": "application/json"}
                mock_request.return_value = mock_response
                
                client.request("GET", "test")
                
                # 验证上下文管理器被调用
                mock_context.assert_called_once()
