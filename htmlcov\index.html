<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">55%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-03 16:50 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_9589fface2250470___init___py.html">nifa\__init__.py</a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838___init___py.html">nifa\api\__init__.py</a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html">nifa\api\client.py</a></td>
                <td>98</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="92 98">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html">nifa\api\data.py</a></td>
                <td>120</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="78 120">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html">nifa\api\info.py</a></td>
                <td>80</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="75 80">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html">nifa\api\judicial.py</a></td>
                <td>88</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="18 88">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html">nifa\api\query_count.py</a></td>
                <td>74</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="17 74">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html">nifa\api\task.py</a></td>
                <td>83</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="51 83">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2___init___py.html">nifa\auth\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html">nifa\auth\encryption.py</a></td>
                <td>112</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="22 112">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html">nifa\auth\signature.py</a></td>
                <td>101</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="62 101">61%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595___init___py.html">nifa\config\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html">nifa\config\enhanced_settings.py</a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html">nifa\config\settings.py</a></td>
                <td>103</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="101 103">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5___init___py.html">nifa\core\__init__.py</a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html">nifa\core\circuit_breaker.py</a></td>
                <td>104</td>
                <td>104</td>
                <td>0</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440___init___py.html">nifa\exceptions\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html">nifa\exceptions\base.py</a></td>
                <td>72</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="67 72">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76___init___py.html">nifa\models\__init__.py</a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html">nifa\models\base.py</a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe___init___py.html">nifa\utils\__init__.py</a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html">nifa\utils\helpers.py</a></td>
                <td>102</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="99 102">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html">nifa\utils\validators.py</a></td>
                <td>78</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="75 78">96%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1436</td>
                <td>652</td>
                <td>0</td>
                <td class="right" data-ratio="784 1436">55%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-03 16:50 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_1b14f832205fdefe_validators_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_9589fface2250470___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
