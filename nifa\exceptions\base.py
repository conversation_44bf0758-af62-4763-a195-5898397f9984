"""
异常处理模块
定义了所有自定义异常类
"""

import time
from typing import Optional, Dict, Any, Type


class NifaError(Exception):
    """NIFA客户端基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.cause = cause  # 原始异常
        self.context = context or {}  # 上下文信息
        self.timestamp = time.time()
        super().__init__(self.message)
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message='{self.message}', error_code='{self.error_code}')"


class NifaAPIError(NifaError):
    """API调用异常"""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None
    ):
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(message, error_code, {"status_code": status_code, "response_data": response_data})


class NifaResponseError(NifaAPIError):
    """API响应异常"""
    
    def __init__(
        self,
        message: str,
        response_code: Optional[str] = None,
        response_message: Optional[str] = None,
        status_code: Optional[int] = None
    ):
        self.response_code = response_code
        self.response_message = response_message
        
        details = {
            "response_code": response_code,
            "response_message": response_message,
            "status_code": status_code
        }
        
        super().__init__(message, status_code, details, response_code)


class NifaSignatureError(NifaError):
    """签名相关异常"""
    pass


class NifaEncryptionError(NifaError):
    """加密相关异常"""
    pass


class NifaValidationError(NifaError):
    """数据验证异常"""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        error_code: Optional[str] = None
    ):
        self.field = field
        self.value = value
        details = {"field": field, "value": value}
        super().__init__(message, error_code, details)


class NifaTimeoutError(NifaError):
    """超时异常"""
    
    def __init__(
        self,
        message: str = "Request timeout",
        timeout: Optional[float] = None,
        error_code: Optional[str] = None
    ):
        self.timeout = timeout
        details = {"timeout": timeout}
        super().__init__(message, error_code, details)


class NifaNetworkError(NifaError):
    """网络异常"""
    
    def __init__(
        self,
        message: str,
        original_error: Optional[Exception] = None,
        error_code: Optional[str] = None
    ):
        self.original_error = original_error
        details = {"original_error": str(original_error) if original_error else None}
        super().__init__(message, error_code, details)


class NifaConfigurationError(NifaError):
    """配置异常"""
    pass


class NifaAuthenticationError(NifaError):
    """认证异常"""
    pass


class NifaRateLimitError(NifaError):
    """限流异常"""
    def __init__(self, message: str, retry_after: Optional[int] = None, **kwargs):
        self.retry_after = retry_after
        super().__init__(message, **kwargs)


class NifaBusinessError(NifaError):
    """业务逻辑异常"""
    pass


class NifaDataError(NifaError):
    """数据异常"""
    pass


class NifaCircuitBreakerError(NifaError):
    """熔断器异常"""
    pass


# 异常映射字典，用于根据响应码映射到具体异常
RESPONSE_CODE_EXCEPTIONS = {
    "0000": None,  # 成功
    "9999": NifaAPIError,  # 系统异常
    "1001": NifaValidationError,  # 参数错误
    "1002": NifaSignatureError,  # 签名错误
    "1003": NifaAuthenticationError,  # 认证失败
    "1004": NifaEncryptionError,  # 加密错误
    "2001": NifaTimeoutError,  # 超时
    "2002": NifaNetworkError,  # 网络错误
    "3001": NifaRateLimitError,  # 限流
    "4001": NifaBusinessError,  # 业务错误
    "5001": NifaDataError,  # 数据错误
}


def create_exception_from_response(
    response_code: str,
    response_message: str,
    status_code: Optional[int] = None,
    response_data: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None
) -> Optional[NifaError]:
    """
    根据响应码创建对应的异常实例
    
    Args:
        response_code: 响应码
        response_message: 响应消息
        status_code: HTTP状态码
        response_data: 响应数据
        
    Returns:
        对应的异常实例
    """
    exception_class = RESPONSE_CODE_EXCEPTIONS.get(response_code, NifaResponseError)
    
    if exception_class is None:
        return None  # 成功响应
    
    if exception_class == NifaResponseError:
        return NifaResponseError(
            message=response_message,
            response_code=response_code,
            response_message=response_message,
            status_code=status_code
        )
    elif issubclass(exception_class, NifaAPIError):
        return exception_class(
            message=response_message,
            status_code=status_code,
            response_data=response_data,
            error_code=response_code
        )
    else:
        return exception_class(
            message=response_message,
            error_code=response_code
        )
