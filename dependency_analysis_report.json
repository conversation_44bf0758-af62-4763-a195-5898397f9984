{"summary": {"total_modules": 23, "total_internal_dependencies": 51, "total_external_dependencies": 22, "circular_dependencies": 0, "dependency_layers": 1}, "modules": ["nifa.api.info", "nifa.api", "nifa.utils", "nifa.exceptions", "nifa.api.judicial", "nifa.models", "nifa.auth.signature", "nifa.exceptions.base", "nifa.config.enhanced_settings", "nifa.core.circuit_breaker", "nifa.auth.encryption", "nifa.api.query_count", "nifa.utils.helpers", "nifa.api.task", "nifa.config.settings", "nifa.api.data", "nifa.api.client", "nifa.config", "nifa.utils.validators", "nifa.core", "nifa", "nifa.models.base", "nifa.auth"], "internal_dependencies": {"nifa": ["nifa.api.info", "nifa.exceptions.base", "nifa.api.task", "nifa.config.settings", "nifa.api.data", "nifa.api.judicial", "nifa.api.query_count"], "nifa.api.client": ["nifa.exceptions.base", "nifa.config.settings", "nifa.auth.signature", "nifa.utils.helpers"], "nifa.api.data": ["nifa.exceptions.base", "nifa.client", "nifa.config.settings", "nifa.auth.encryption", "nifa.utils.validators"], "nifa.api.info": ["nifa.exceptions.base", "nifa.utils.validators", "nifa.client"], "nifa.api.judicial": ["nifa.exceptions.base", "nifa.utils.validators", "nifa.client"], "nifa.api.query_count": ["nifa.exceptions.base", "nifa.utils.validators", "nifa.client"], "nifa.api.task": ["nifa.exceptions.base", "nifa.utils.validators", "nifa.client"], "nifa.api": ["nifa.judicial", "nifa.data", "nifa.query_count", "nifa.info", "nifa.task"], "nifa.auth.encryption": ["nifa.exceptions.base"], "nifa.auth.signature": ["nifa.exceptions.base", "nifa.config.settings", "nifa.utils.helpers"], "nifa.auth": ["nifa.signature", "nifa.encryption"], "nifa.config": ["nifa.settings"], "nifa.core": ["nifa.retry", "nifa.circuit_breaker", "nifa.client", "nifa.connection_pool"], "nifa.exceptions": ["nifa.base"], "nifa.models": ["nifa.info", "nifa.base"], "nifa.utils.helpers": ["nifa.exceptions.base"], "nifa.utils.validators": ["nifa.exceptions.base"], "nifa.utils": ["nifa.validators", "nifa.helpers"]}, "external_dependencies": {"logging": 10, "contextlib": 1, "requests": 3, "typing": 15, "time": 5, "os": 3, "base64": 2, "json": 3, "gmssl": 3, "random": 1, "string": 1, "hmac": 1, "functools": 3, "hashlib": 2, "pydantic": 3, "pathlib": 2, "pydantic_settings": 2, "enum": 3, "threading": 1, "dataclasses": 1, "datetime": 3, "re": 2}, "circular_dependencies": [], "dependency_layers": [["nifa.api", "nifa.utils", "nifa.exceptions", "nifa.models", "nifa.config.enhanced_settings", "nifa.core.circuit_breaker", "nifa.api.client", "nifa.config", "nifa.core", "nifa", "nifa.models.base", "nifa.auth"]], "coupling_analysis": {"nifa.api.info": {"afferent_coupling": 1, "efferent_coupling": 2, "instability": 0.6666666666666666, "dependencies": ["nifa.exceptions.base", "nifa.utils.validators"], "dependents": ["nifa"]}, "nifa.api": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.utils": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.exceptions": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.api.judicial": {"afferent_coupling": 1, "efferent_coupling": 2, "instability": 0.6666666666666666, "dependencies": ["nifa.exceptions.base", "nifa.utils.validators"], "dependents": ["nifa"]}, "nifa.models": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.auth.signature": {"afferent_coupling": 1, "efferent_coupling": 3, "instability": 0.75, "dependencies": ["nifa.exceptions.base", "nifa.config.settings", "nifa.utils.helpers"], "dependents": ["nifa.api.client"]}, "nifa.exceptions.base": {"afferent_coupling": 11, "efferent_coupling": 0, "instability": 0.0, "dependencies": [], "dependents": ["nifa.api.info", "nifa", "nifa.utils.helpers", "nifa.api.task", "nifa.api.data", "nifa.api.client", "nifa.api.judicial", "nifa.auth.signature", "nifa.utils.validators", "nifa.auth.encryption", "nifa.api.query_count"]}, "nifa.config.enhanced_settings": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.core.circuit_breaker": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.auth.encryption": {"afferent_coupling": 1, "efferent_coupling": 1, "instability": 0.5, "dependencies": ["nifa.exceptions.base"], "dependents": ["nifa.api.data"]}, "nifa.api.query_count": {"afferent_coupling": 1, "efferent_coupling": 2, "instability": 0.6666666666666666, "dependencies": ["nifa.exceptions.base", "nifa.utils.validators"], "dependents": ["nifa"]}, "nifa.utils.helpers": {"afferent_coupling": 2, "efferent_coupling": 1, "instability": 0.3333333333333333, "dependencies": ["nifa.exceptions.base"], "dependents": ["nifa.api.client", "nifa.auth.signature"]}, "nifa.api.task": {"afferent_coupling": 1, "efferent_coupling": 2, "instability": 0.6666666666666666, "dependencies": ["nifa.exceptions.base", "nifa.utils.validators"], "dependents": ["nifa"]}, "nifa.config.settings": {"afferent_coupling": 4, "efferent_coupling": 0, "instability": 0.0, "dependencies": [], "dependents": ["nifa.api.data", "nifa.api.client", "nifa", "nifa.auth.signature"]}, "nifa.api.data": {"afferent_coupling": 1, "efferent_coupling": 4, "instability": 0.8, "dependencies": ["nifa.exceptions.base", "nifa.config.settings", "nifa.auth.encryption", "nifa.utils.validators"], "dependents": ["nifa"]}, "nifa.api.client": {"afferent_coupling": 0, "efferent_coupling": 4, "instability": 1.0, "dependencies": ["nifa.exceptions.base", "nifa.config.settings", "nifa.auth.signature", "nifa.utils.helpers"], "dependents": []}, "nifa.config": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.utils.validators": {"afferent_coupling": 5, "efferent_coupling": 1, "instability": 0.16666666666666666, "dependencies": ["nifa.exceptions.base"], "dependents": ["nifa.api.info", "nifa.api.task", "nifa.api.data", "nifa.api.judicial", "nifa.api.query_count"]}, "nifa.core": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa": {"afferent_coupling": 0, "efferent_coupling": 7, "instability": 1.0, "dependencies": ["nifa.api.info", "nifa.exceptions.base", "nifa.api.task", "nifa.config.settings", "nifa.api.data", "nifa.api.judicial", "nifa.api.query_count"], "dependents": []}, "nifa.models.base": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}, "nifa.auth": {"afferent_coupling": 0, "efferent_coupling": 0, "instability": 0, "dependencies": [], "dependents": []}}}