"""
签名认证测试
测试请求签名和验证功能
"""

import pytest
import hashlib
import time
from unittest.mock import Mock, patch

from nifa.auth.signature import RequestSigner
from nifa.exceptions.base import NifaSignatureError


class TestRequestSigner:
    """请求签名器测试类"""
    
    def test_init_with_defaults(self):
        """测试使用默认参数初始化"""
        signer = RequestSigner(org_code="TEST123")
        assert signer.org_code == "TEST123"
        assert signer.algorithm == "SHA256"
    
    def test_init_with_custom_algorithm(self):
        """测试使用自定义算法初始化"""
        signer = RequestSigner(org_code="TEST123", algorithm="MD5")
        assert signer.algorithm == "MD5"
    
    def test_generate_timestamp(self):
        """测试时间戳生成"""
        signer = RequestSigner(org_code="TEST123")
        
        timestamp1 = signer._generate_timestamp()
        time.sleep(0.1)
        timestamp2 = signer._generate_timestamp()
        
        # 验证时间戳格式和递增性
        assert len(timestamp1) == 13  # 毫秒时间戳长度
        assert timestamp1.isdigit()
        assert int(timestamp2) > int(timestamp1)
    
    def test_generate_nonce(self):
        """测试随机数生成"""
        signer = RequestSigner(org_code="TEST123")
        
        nonce1 = signer._generate_nonce()
        nonce2 = signer._generate_nonce()
        
        # 验证随机数格式和唯一性
        assert len(nonce1) == 32  # UUID去掉连字符的长度
        assert len(nonce2) == 32
        assert nonce1 != nonce2
        assert nonce1.isalnum()
        assert nonce2.isalnum()
    
    def test_create_signature_string(self):
        """测试签名字符串创建"""
        signer = RequestSigner(org_code="TEST123")
        
        params = {
            "param1": "value1",
            "param2": "value2",
            "timestamp": "1234567890123",
            "nonce": "test_nonce"
        }
        
        signature_string = signer._create_signature_string(params)
        
        # 验证签名字符串格式
        assert "param1=value1" in signature_string
        assert "param2=value2" in signature_string
        assert "timestamp=1234567890123" in signature_string
        assert "nonce=test_nonce" in signature_string
        
        # 验证参数按字母顺序排列
        parts = signature_string.split("&")
        sorted_parts = sorted(parts)
        assert parts == sorted_parts
    
    def test_create_signature_string_with_special_chars(self):
        """测试包含特殊字符的签名字符串"""
        signer = RequestSigner(org_code="TEST123")
        
        params = {
            "param1": "value with spaces",
            "param2": "value&with&ampersand",
            "param3": "value=with=equals"
        }
        
        signature_string = signer._create_signature_string(params)
        
        # 验证特殊字符被正确处理
        assert "value%20with%20spaces" in signature_string
        assert "value%26with%26ampersand" in signature_string
        assert "value%3Dwith%3Dequals" in signature_string
    
    def test_calculate_signature_sha256(self):
        """测试SHA256签名计算"""
        signer = RequestSigner(org_code="TEST123", algorithm="SHA256")
        
        signature_string = "param1=value1&param2=value2"
        signature = signer._calculate_signature(signature_string)
        
        # 验证签名格式
        assert len(signature) == 64  # SHA256十六进制长度
        assert signature.isalnum()
        
        # 验证签名一致性
        signature2 = signer._calculate_signature(signature_string)
        assert signature == signature2
    
    def test_calculate_signature_md5(self):
        """测试MD5签名计算"""
        signer = RequestSigner(org_code="TEST123", algorithm="MD5")
        
        signature_string = "param1=value1&param2=value2"
        signature = signer._calculate_signature(signature_string)
        
        # 验证签名格式
        assert len(signature) == 32  # MD5十六进制长度
        assert signature.isalnum()
    
    def test_calculate_signature_unsupported_algorithm(self):
        """测试不支持的签名算法"""
        signer = RequestSigner(org_code="TEST123", algorithm="UNSUPPORTED")
        
        with pytest.raises(NifaSignatureError) as exc_info:
            signer._calculate_signature("test_string")
        assert "不支持的签名算法" in str(exc_info.value)
    
    def test_sign_request_data(self):
        """测试请求数据签名"""
        signer = RequestSigner(org_code="TEST123")
        
        original_data = {
            "param1": "value1",
            "param2": "value2"
        }
        
        signed_data = signer.sign_request_data(original_data)
        
        # 验证签名字段被添加
        assert "timestamp" in signed_data
        assert "nonce" in signed_data
        assert "signature" in signed_data
        assert "orgCode" in signed_data
        
        # 验证原始数据保持不变
        assert signed_data["param1"] == "value1"
        assert signed_data["param2"] == "value2"
        assert signed_data["orgCode"] == "TEST123"
        
        # 验证签名字段格式
        assert len(signed_data["timestamp"]) == 13
        assert len(signed_data["nonce"]) == 32
        assert len(signed_data["signature"]) in [32, 64]  # MD5或SHA256
    
    def test_sign_request_data_empty(self):
        """测试空数据签名"""
        signer = RequestSigner(org_code="TEST123")
        
        signed_data = signer.sign_request_data({})
        
        # 验证即使空数据也会添加签名字段
        assert "timestamp" in signed_data
        assert "nonce" in signed_data
        assert "signature" in signed_data
        assert "orgCode" in signed_data
    
    def test_sign_request_data_with_existing_signature_fields(self):
        """测试包含现有签名字段的数据"""
        signer = RequestSigner(org_code="TEST123")
        
        original_data = {
            "param1": "value1",
            "timestamp": "old_timestamp",
            "signature": "old_signature"
        }
        
        signed_data = signer.sign_request_data(original_data)
        
        # 验证签名字段被覆盖
        assert signed_data["timestamp"] != "old_timestamp"
        assert signed_data["signature"] != "old_signature"
        assert signed_data["param1"] == "value1"
    
    def test_verify_signature_valid(self):
        """测试有效签名验证"""
        signer = RequestSigner(org_code="TEST123")
        
        # 创建签名数据
        original_data = {"param1": "value1"}
        signed_data = signer.sign_request_data(original_data)
        
        # 验证签名
        is_valid = signer.verify_signature(signed_data)
        assert is_valid is True
    
    def test_verify_signature_invalid(self):
        """测试无效签名验证"""
        signer = RequestSigner(org_code="TEST123")
        
        # 创建无效签名数据
        invalid_data = {
            "param1": "value1",
            "timestamp": "1234567890123",
            "nonce": "test_nonce",
            "signature": "invalid_signature",
            "orgCode": "TEST123"
        }
        
        # 验证签名
        is_valid = signer.verify_signature(invalid_data)
        assert is_valid is False
    
    def test_verify_signature_missing_fields(self):
        """测试缺少签名字段的验证"""
        signer = RequestSigner(org_code="TEST123")
        
        # 缺少签名字段的数据
        incomplete_data = {
            "param1": "value1",
            "timestamp": "1234567890123"
            # 缺少nonce和signature
        }
        
        with pytest.raises(NifaSignatureError) as exc_info:
            signer.verify_signature(incomplete_data)
        assert "缺少必要的签名字段" in str(exc_info.value)
    
    def test_verify_signature_wrong_org_code(self):
        """测试错误机构代码的签名验证"""
        signer = RequestSigner(org_code="TEST123")
        
        # 创建签名数据
        original_data = {"param1": "value1"}
        signed_data = signer.sign_request_data(original_data)
        
        # 修改机构代码
        signed_data["orgCode"] = "WRONG123"
        
        # 验证签名
        is_valid = signer.verify_signature(signed_data)
        assert is_valid is False
    
    @patch('nifa.auth.signature.time.time')
    def test_verify_signature_expired(self, mock_time):
        """测试过期签名验证"""
        # 设置当前时间
        current_time = 1234567890.0
        mock_time.return_value = current_time
        
        signer = RequestSigner(org_code="TEST123")
        
        # 创建过期的签名数据（5分钟前）
        expired_data = {
            "param1": "value1",
            "timestamp": str(int((current_time - 300) * 1000)),  # 5分钟前
            "nonce": "test_nonce",
            "orgCode": "TEST123"
        }
        
        # 手动计算签名
        signature_string = signer._create_signature_string(expired_data)
        signature = signer._calculate_signature(signature_string)
        expired_data["signature"] = signature
        
        # 验证过期签名
        is_valid = signer.verify_signature(expired_data)
        assert is_valid is False
    
    def test_sign_and_verify_round_trip(self):
        """测试签名和验证的完整流程"""
        signer = RequestSigner(org_code="TEST123")
        
        # 测试多种数据类型
        test_cases = [
            {"simple": "value"},
            {"multiple": "params", "with": "values"},
            {"number": 123, "boolean": True},
            {"chinese": "中文测试", "special": "!@#$%^&*()"}
        ]
        
        for original_data in test_cases:
            # 签名
            signed_data = signer.sign_request_data(original_data.copy())
            
            # 验证
            is_valid = signer.verify_signature(signed_data)
            assert is_valid is True, f"Failed for data: {original_data}"
