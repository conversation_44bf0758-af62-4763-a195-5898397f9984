"""
端到端测试
测试完整的业务流程和用户场景
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch, call

from nifa.api.info import InfoAPI
from nifa.api.data import DataAPI
from nifa.api.task import TaskAPI
from nifa.exceptions.base import NifaValidationError, NifaAPIError


class TestEndToEnd:
    """端到端测试类"""
    
    def test_complete_credit_query_workflow(self):
        """测试完整的信用查询工作流程"""
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟成功响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '''
            {
                "code": "0000",
                "message": "查询成功",
                "data": {
                    "personalInfo": {
                        "name": "张三",
                        "idCard": "110101199001011237"
                    },
                    "creditInfo": {
                        "score": 750,
                        "level": "优秀"
                    }
                }
            }
            '''
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            # 创建信息查询API实例
            info_api = InfoAPI()

            # 执行信用查询
            result = info_api.query_personal_credit(
                id_card="110101199001011237",
                name="张三"
            )
            
            # 验证结果
            assert result["code"] == "0000"
            assert result["data"]["personalInfo"]["name"] == "张三"
            assert result["data"]["creditInfo"]["score"] == 750
            
            # 验证请求被正确发送
            mock_request.assert_called_once()
            call_args = mock_request.call_args
            
            # 验证请求方法和URL
            assert call_args[0][0] == "POST"
            assert "personal/credit" in call_args[0][1]
            
            # 验证请求包含签名
            request_data = call_args[1]["json"]
            assert "orgCode" in request_data
            assert "timestamp" in request_data
            assert "sign" in request_data
    
    def test_data_upload_and_status_tracking_workflow(self):
        """测试数据上报和状态跟踪工作流程"""
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟上报成功响应
            upload_response = Mock()
            upload_response.status_code = 200
            upload_response.text = '''
            {
                "code": "0000",
                "message": "上报成功",
                "data": {
                    "taskId": "TASK123456789",
                    "status": "processing"
                }
            }
            '''
            upload_response.headers = {"Content-Type": "application/json"}
            
            # 模拟状态查询响应
            status_response = Mock()
            status_response.status_code = 200
            status_response.text = '''
            {
                "code": "0000",
                "message": "查询成功",
                "data": {
                    "taskId": "TASK123456789",
                    "status": "completed",
                    "progress": 100,
                    "result": "处理完成"
                }
            }
            '''
            status_response.headers = {"Content-Type": "application/json"}
            
            # 设置响应序列
            mock_request.side_effect = [upload_response, status_response]
            
            # 创建数据API和任务API实例
            data_api = DataAPI()
            task_api = TaskAPI()

            # 1. 上报数据
            upload_result = data_api.upload_data(
                data_type="loan_application",
                data_content={
                    "applicant": "张三",
                    "amount": 100000,
                    "term": 36
                }
            )

            # 验证上报结果
            assert upload_result["code"] == "0000"
            task_id = upload_result["data"]["taskId"]
            assert task_id == "TASK123456789"

            # 2. 查询处理状态
            status_result = task_api.query_task_status(task_id=task_id)
            
            # 验证状态查询结果
            assert status_result["code"] == "0000"
            assert status_result["data"]["status"] == "completed"
            assert status_result["data"]["progress"] == 100
            
            # 验证两次请求都被发送
            assert mock_request.call_count == 2
    
    def test_file_upload_workflow(self):
        """测试文件上传工作流程"""
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟文件上传成功响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '''
            {
                "code": "0000",
                "message": "文件上传成功",
                "data": {
                    "fileId": "FILE123456789",
                    "fileName": "test_data.csv",
                    "fileSize": 1024,
                    "uploadTime": "2023-12-25 10:30:00"
                }
            }
            '''
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            # 创建临时文件
            test_content = "姓名,身份证号,金额\n张三,110101199001011237,10000\n李四,110101199001011245,20000"
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as f:
                f.write(test_content)
                temp_file_path = f.name
            
            try:
                # 创建数据API实例
                data_api = DataAPI()

                # 上传文件
                result = data_api.upload_file(
                    file_path=temp_file_path,
                    file_type="csv"
                )
                
                # 验证结果
                assert result["code"] == "0000"
                assert result["data"]["fileId"] == "FILE123456789"
                assert result["data"]["fileName"] == "test_data.csv"
                
                # 验证请求包含文件内容
                call_args = mock_request.call_args
                request_data = call_args[1]["json"]
                assert "fileContent" in request_data
                assert "fileName" in request_data
                assert "fileSize" in request_data
                
            finally:
                # 清理临时文件
                os.unlink(temp_file_path)
    
    def test_batch_query_workflow(self):
        """测试批量查询工作流程"""
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟批量查询成功响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '''
            {
                "code": "0000",
                "message": "批量查询成功",
                "data": {
                    "batchId": "BATCH123456789",
                    "total": 2,
                    "success": 2,
                    "failed": 0,
                    "results": [
                        {
                            "idCard": "110101199001011237",
                            "name": "张三",
                            "creditScore": 750,
                            "status": "success"
                        },
                        {
                            "idCard": "110101199001011245",
                            "name": "李四",
                            "creditScore": 680,
                            "status": "success"
                        }
                    ]
                }
            }
            '''
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            # 创建信息查询API实例
            info_api = InfoAPI()

            # 准备批量查询数据
            query_list = [
                {"idCard": "110101199001011237", "name": "张三"},
                {"idCard": "110101199001011245", "name": "李四"}
            ]

            # 执行批量查询
            result = info_api.batch_query_personal_credit(query_list=query_list)
            
            # 验证结果
            assert result["code"] == "0000"
            assert result["data"]["total"] == 2
            assert result["data"]["success"] == 2
            assert len(result["data"]["results"]) == 2
            
            # 验证第一个结果
            first_result = result["data"]["results"][0]
            assert first_result["name"] == "张三"
            assert first_result["creditScore"] == 750
            
            # 验证请求数据
            call_args = mock_request.call_args
            request_data = call_args[1]["json"]
            assert "queryList" in request_data
            assert len(request_data["queryList"]) == 2
    
    def test_error_handling_workflow(self):
        """测试错误处理工作流程"""
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟API错误响应
            mock_response = Mock()
            mock_response.status_code = 400
            mock_response.text = '''
            {
                "code": "4001",
                "message": "身份证号码格式错误",
                "data": null
            }
            '''
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            # 创建信息查询API实例
            info_api = InfoAPI()

            # 验证API错误被正确处理
            with pytest.raises(NifaAPIError) as exc_info:
                info_api.query_personal_credit(
                    id_card="invalid_id",
                    name="张三"
                )
            
            assert "4001" in str(exc_info.value)
            assert "身份证号码格式错误" in str(exc_info.value)
    
    def test_validation_error_workflow(self):
        """测试数据验证错误工作流程"""
        # 创建信息查询API实例
        info_api = InfoAPI()

        # 测试身份证验证错误
        with pytest.raises(NifaValidationError) as exc_info:
            info_api.query_personal_credit(
                id_card="123456",  # 无效身份证
                name="张三"
            )

        assert "身份证号码格式不正确" in str(exc_info.value)

        # 测试姓名验证错误
        with pytest.raises(NifaValidationError) as exc_info:
            info_api.query_personal_credit(
                id_card="110101199001011237",
                name=""  # 空姓名
            )
        
        assert "姓名不能为空" in str(exc_info.value)
    
    def test_configuration_workflow(self):
        """测试配置工作流程"""
        # 测试自定义配置
        with patch.dict(os.environ, {
            'NIFA_BASE_URL': 'https://custom.api.com',
            'NIFA_ORG_CODE': 'CUSTOM123',
            'NIFA_TIMEOUT': '60'
        }):
            with patch('nifa.api.client.requests.Session.request') as mock_request:
                # 模拟成功响应
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.text = '{"code": "0000", "message": "成功"}'
                mock_response.headers = {"Content-Type": "application/json"}
                mock_request.return_value = mock_response
                
                # 创建信息查询API实例（使用环境变量配置）
                info_api = InfoAPI()

                # 执行请求
                info_api.query_personal_credit(
                    id_card="110101199001011237",
                    name="张三"
                )
                
                # 验证使用了自定义配置
                call_args = mock_request.call_args
                assert "custom.api.com" in call_args[0][1]  # URL包含自定义域名
                
                # 验证机构代码
                request_data = call_args[1]["json"]
                assert request_data["orgCode"] == "CUSTOM123"
    
    def test_context_manager_workflow(self):
        """测试上下文管理器工作流程"""
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟成功响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '{"code": "0000", "message": "成功"}'
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            # 使用上下文管理器
            with InfoAPI() as info_api:
                # 在上下文中执行操作
                result = info_api.query_personal_credit(
                    id_card="110101199001011237",
                    name="张三"
                )

                assert result["code"] == "0000"

            # 验证资源被正确清理
    
    def test_concurrent_requests_workflow(self):
        """测试并发请求工作流程"""
        import threading
        import time
        
        with patch('nifa.api.client.requests.Session.request') as mock_request:
            # 模拟成功响应
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.text = '{"code": "0000", "message": "成功"}'
            mock_response.headers = {"Content-Type": "application/json"}
            mock_request.return_value = mock_response
            
            # 创建信息查询API实例
            info_api = InfoAPI()

            results = []
            errors = []

            def make_request(index):
                try:
                    result = info_api.query_personal_credit(
                        id_card="110101199001011237",
                        name=f"用户{index}"
                    )
                    results.append(result)
                except Exception as e:
                    errors.append(e)
            
            # 创建多个线程并发请求
            threads = []
            for i in range(5):
                thread = threading.Thread(target=make_request, args=(i,))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 验证结果
            assert len(results) == 5
            assert len(errors) == 0
            assert mock_request.call_count == 5
            
            # 验证所有请求都成功
            for result in results:
                assert result["code"] == "0000"
