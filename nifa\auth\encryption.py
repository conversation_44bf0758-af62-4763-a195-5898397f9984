"""
加密模块
实现SM2/SM4等国密算法加密功能
"""

import base64
import logging
from typing import Optional, Union

try:
    from gmssl import sm2, sm4
    from gmssl.sm2 import CryptSM2
    from gmssl.sm4 import CryptSM4
    GMSSL_AVAILABLE = True
except ImportError:
    GMSSL_AVAILABLE = False
    sm2 = None
    sm4 = None
    CryptSM2 = None
    CryptSM4 = None

from ..exceptions.base import NifaEncryptionError

logger = logging.getLogger(__name__)


class SM2Encryption:
    """SM2加密类"""
    
    def __init__(self, public_key: Optional[str] = None, private_key: Optional[str] = None):
        """
        初始化SM2加密
        
        Args:
            public_key: SM2公钥（十六进制字符串）
            private_key: SM2私钥（十六进制字符串）
        """
        if not GMSSL_AVAILABLE:
            raise NifaEncryptionError("gmssl库未安装，无法使用SM2加密功能")
        
        self.public_key = public_key
        self.private_key = private_key
        
        # 初始化SM2实例
        if public_key and private_key:
            self.sm2_crypt = CryptSM2(public_key=public_key, private_key=private_key)
        elif public_key:
            self.sm2_crypt = CryptSM2(public_key=public_key, private_key=None)
        elif private_key:
            self.sm2_crypt = CryptSM2(public_key=None, private_key=private_key)
        else:
            self.sm2_crypt = None
    
    def encrypt(self, plaintext: Union[str, bytes]) -> str:
        """
        SM2加密
        
        Args:
            plaintext: 明文
            
        Returns:
            Base64编码的密文
            
        Raises:
            NifaEncryptionError: 加密失败时抛出
        """
        if not self.sm2_crypt or not self.public_key:
            raise NifaEncryptionError("SM2公钥未设置，无法进行加密")
        
        try:
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
            
            # SM2加密
            ciphertext = self.sm2_crypt.encrypt(plaintext)
            
            # Base64编码
            return base64.b64encode(ciphertext).decode('utf-8')
            
        except Exception as e:
            logger.error(f"SM2加密失败: {str(e)}")
            raise NifaEncryptionError(f"SM2加密失败: {str(e)}") from e
    
    def decrypt(self, ciphertext: str) -> str:
        """
        SM2解密
        
        Args:
            ciphertext: Base64编码的密文
            
        Returns:
            明文
            
        Raises:
            NifaEncryptionError: 解密失败时抛出
        """
        if not self.sm2_crypt or not self.private_key:
            raise NifaEncryptionError("SM2私钥未设置，无法进行解密")
        
        try:
            # Base64解码
            ciphertext_bytes = base64.b64decode(ciphertext)
            
            # SM2解密
            plaintext_bytes = self.sm2_crypt.decrypt(ciphertext_bytes)
            
            return plaintext_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error(f"SM2解密失败: {str(e)}")
            raise NifaEncryptionError(f"SM2解密失败: {str(e)}") from e
    
    def sign(self, data: Union[str, bytes]) -> str:
        """
        SM2签名
        
        Args:
            data: 待签名数据
            
        Returns:
            Base64编码的签名
            
        Raises:
            NifaEncryptionError: 签名失败时抛出
        """
        if not self.sm2_crypt or not self.private_key:
            raise NifaEncryptionError("SM2私钥未设置，无法进行签名")
        
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # SM2签名
            signature = self.sm2_crypt.sign(data, random_hex_str=None)
            
            # Base64编码
            return base64.b64encode(signature).decode('utf-8')
            
        except Exception as e:
            logger.error(f"SM2签名失败: {str(e)}")
            raise NifaEncryptionError(f"SM2签名失败: {str(e)}") from e
    
    def verify(self, data: Union[str, bytes], signature: str) -> bool:
        """
        SM2验签
        
        Args:
            data: 原始数据
            signature: Base64编码的签名
            
        Returns:
            验签结果
            
        Raises:
            NifaEncryptionError: 验签失败时抛出
        """
        if not self.sm2_crypt or not self.public_key:
            raise NifaEncryptionError("SM2公钥未设置，无法进行验签")
        
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Base64解码签名
            signature_bytes = base64.b64decode(signature)
            
            # SM2验签
            return self.sm2_crypt.verify(signature_bytes, data)
            
        except Exception as e:
            logger.error(f"SM2验签失败: {str(e)}")
            raise NifaEncryptionError(f"SM2验签失败: {str(e)}") from e


class SM4Encryption:
    """SM4加密类"""
    
    def __init__(self, key: Optional[str] = None):
        """
        初始化SM4加密
        
        Args:
            key: SM4密钥（十六进制字符串，32字符）
        """
        if not GMSSL_AVAILABLE:
            raise NifaEncryptionError("gmssl库未安装，无法使用SM4加密功能")
        
        self.key = key
        
        if key:
            if len(key) != 32:
                raise NifaEncryptionError("SM4密钥长度必须为32个十六进制字符")
            
            try:
                # 验证密钥格式
                bytes.fromhex(key)
                self.sm4_crypt = CryptSM4()
            except ValueError:
                raise NifaEncryptionError("SM4密钥格式不正确，必须为十六进制字符串")
        else:
            self.sm4_crypt = None
    
    def encrypt(self, plaintext: Union[str, bytes]) -> str:
        """
        SM4加密
        
        Args:
            plaintext: 明文
            
        Returns:
            Base64编码的密文
            
        Raises:
            NifaEncryptionError: 加密失败时抛出
        """
        if not self.sm4_crypt or not self.key:
            raise NifaEncryptionError("SM4密钥未设置，无法进行加密")
        
        try:
            if isinstance(plaintext, str):
                plaintext = plaintext.encode('utf-8')
            
            # SM4加密
            ciphertext = self.sm4_crypt.crypt_ecb(plaintext, self.key)
            
            # Base64编码
            return base64.b64encode(ciphertext).decode('utf-8')
            
        except Exception as e:
            logger.error(f"SM4加密失败: {str(e)}")
            raise NifaEncryptionError(f"SM4加密失败: {str(e)}") from e
    
    def decrypt(self, ciphertext: str) -> str:
        """
        SM4解密
        
        Args:
            ciphertext: Base64编码的密文
            
        Returns:
            明文
            
        Raises:
            NifaEncryptionError: 解密失败时抛出
        """
        if not self.sm4_crypt or not self.key:
            raise NifaEncryptionError("SM4密钥未设置，无法进行解密")
        
        try:
            # Base64解码
            ciphertext_bytes = base64.b64decode(ciphertext)
            
            # SM4解密
            plaintext_bytes = self.sm4_crypt.crypt_ecb(ciphertext_bytes, self.key)
            
            # 去除填充
            plaintext = plaintext_bytes.rstrip(b'\x00')
            
            return plaintext.decode('utf-8')
            
        except Exception as e:
            logger.error(f"SM4解密失败: {str(e)}")
            raise NifaEncryptionError(f"SM4解密失败: {str(e)}") from e


def create_sm2_encryption(public_key: Optional[str] = None, private_key: Optional[str] = None) -> SM2Encryption:
    """
    创建SM2加密实例
    
    Args:
        public_key: SM2公钥
        private_key: SM2私钥
        
    Returns:
        SM2加密实例
    """
    return SM2Encryption(public_key, private_key)


def create_sm4_encryption(key: Optional[str] = None) -> SM4Encryption:
    """
    创建SM4加密实例
    
    Args:
        key: SM4密钥
        
    Returns:
        SM4加密实例
    """
    return SM4Encryption(key)
