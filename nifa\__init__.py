"""
NIFA Credit API Client
~~~~~~~~~~~~~~~~~~~~~

A Python client for the NIFA Credit Information Sharing Platform.
互联网金融信息共享平台接口Python客户端

:copyright: (c) 2023
:license: MIT
"""

__version__ = "1.0.0"
__author__ = "NIFA Team"
__email__ = "<EMAIL>"

# 导入主要的API类
from .api.info import InfoAPI
from .api.data import DataAPI
from .api.task import TaskAPI
from .api.query_count import QueryCountAPI
from .api.judicial import JudicialAPI

# 导入异常类
from .exceptions.base import (
    NifaError,
    NifaAPIError,
    NifaResponseError,
    NifaSignatureError,
    NifaEncryptionError,
    NifaValidationError,
    NifaTimeoutError,
    NifaNetworkError,
)

# 导入配置类
from .config.settings import Settings

__all__ = [
    # API Classes
    "InfoAPI",
    "DataAPI", 
    "TaskAPI",
    "QueryCountAPI",
    "JudicialAPI",
    
    # Exception Classes
    "NifaError",
    "NifaAPIError",
    "NifaResponseError",
    "NifaSignatureError",
    "NifaEncryptionError",
    "NifaValidationError",
    "NifaTimeoutError",
    "NifaNetworkError",
    
    # Configuration
    "Settings",
]
