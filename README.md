# NIFA Credit API Client

互联网金融信息共享平台接口Python客户端

[![Python Version](https://img.shields.io/badge/python-3.12+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## 简介

NIFA Credit API Client 是一个用于访问互联网金融信息共享平台（NIFA）API的Python客户端库。该库提供了简洁、易用的接口来进行信用信息查询、数据上报、查询量统计等操作。

## 特性

- 🚀 **简洁易用**: Pythonic的API设计，易于学习和使用
- 🔒 **安全可靠**: 支持国密算法（SM2/SM4）加密和签名验证
- 🛡️ **异常处理**: 完善的异常处理机制和错误信息
- 📊 **数据验证**: 内置数据验证，确保请求参数正确性
- 🔄 **重试机制**: 自动重试和超时控制
- 📝 **详细日志**: 完整的请求响应日志记录
- 🧪 **测试覆盖**: 全面的单元测试和集成测试
- 📖 **文档完善**: 详细的API文档和使用示例

## 支持的API

- ✅ 个人信用信息查询
- ✅ 企业信用信息查询
- ✅ 批量信用信息查询
- ✅ 数据上报和文件上传
- ✅ 查询量耗用统计
- ✅ 报送状态查询
- ✅ 司法数据查询
- ✅ 查询历史记录

## 安装

### 使用 uv（推荐）

```bash
uv add nifa-client
```

### 使用 pip

```bash
pip install nifa-client
```

## 快速开始

### 1. 环境配置

创建 `.env` 文件：

```env
NIFA_ORG_CODE=your_organization_code
NIFA_BASE_URL=https://api.nifa.org.cn
NIFA_TIMEOUT=30
NIFA_MAX_RETRIES=3
```

### 2. 基础使用

```python
import os
from nifa import InfoAPI

# 设置环境变量
os.environ["NIFA_ORG_CODE"] = "YOUR_ORG_CODE"

# 创建API客户端
with InfoAPI() as info_api:
    # 查询个人信用信息
    result = info_api.query_personal_credit(
        id_card="110101199001011234",
        name="张三",
        query_type="01"
    )
    print(result)
```

## 详细使用指南

### 信用信息查询

#### 个人信用查询

```python
from nifa import InfoAPI

with InfoAPI() as info_api:
    # 基础信息查询
    result = info_api.query_personal_credit(
        id_card="110101199001011234",
        name="张三",
        query_type="01",  # 01-基础信息，02-详细信息
        query_reason="贷款申请信用查询"
    )

    # 批量查询
    query_list = [
        {"idCard": "110101199001011234", "name": "张三"},
        {"idCard": "110101199001011235", "name": "李四"}
    ]
    batch_result = info_api.batch_query_personal_credit(
        query_list=query_list,
        query_type="01"
    )
```

#### 企业信用查询

```python
from nifa import InfoAPI

with InfoAPI() as info_api:
    result = info_api.query_enterprise_credit(
        org_code="91110000123456789X",
        org_name="示例企业有限公司",
        query_type="02"
    )
```

### 数据上报

```python
from nifa import DataAPI

with DataAPI() as data_api:
    # 上报单条数据
    result = data_api.upload_data(
        data_type="loan_info",
        data_content={
            "customerName": "张三",
            "idCard": "110101199001011234",
            "loanAmount": "100000.00",
            "loanDate": "2023-01-01"
        },
        encrypt=False  # 是否加密
    )

    # 文件上传
    file_result = data_api.upload_file(
        file_path="data.csv",
        file_type="csv",
        description="贷款数据文件"
    )
```

### 司法数据查询

```python
from nifa import JudicialAPI

with JudicialAPI() as judicial_api:
    # 查询法院记录
    court_result = judicial_api.query_court_records(
        id_card="110101199001011234",
        name="张三",
        query_type="all"  # all, civil, criminal, administrative
    )

    # 查询失信记录
    dishonest_result = judicial_api.query_dishonest_records(
        id_card="110101199001011234",
        name="张三"
    )
```

### 查询量统计

```python
from nifa import QueryCountAPI

with QueryCountAPI() as query_api:
    # 获取查询量统计
    count_result = query_api.get_query_count(
        start_date="2023-01-01",
        end_date="2023-12-31"
    )

    # 获取剩余配额
    quota_result = query_api.get_remaining_quota(
        quota_type="monthly"
    )
```

## 配置选项

### 环境变量配置

| 变量名 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `NIFA_ORG_CODE` | 机构代码 | - | ✅ |
| `NIFA_BASE_URL` | API基础URL | `https://api.nifa.org.cn` | ❌ |
| `NIFA_TIMEOUT` | 请求超时时间（秒） | `30` | ❌ |
| `NIFA_MAX_RETRIES` | 最大重试次数 | `3` | ❌ |
| `NIFA_RETRY_DELAY` | 重试延迟时间（秒） | `1.0` | ❌ |
| `NIFA_SM2_PUBLIC_KEY` | SM2公钥 | - | ❌ |
| `NIFA_SM2_PRIVATE_KEY` | SM2私钥 | - | ❌ |
| `NIFA_SM4_KEY` | SM4密钥 | - | ❌ |

### 代码配置

```python
from nifa.config.settings import Settings
from nifa.api.client import APIClient
from nifa import InfoAPI

# 自定义配置
settings = Settings(
    NIFA_ORG_CODE="YOUR_ORG_CODE",
    NIFA_BASE_URL="https://test-api.nifa.org.cn",
    NIFA_TIMEOUT=60,
    ENVIRONMENT="production"
)

# 自定义客户端
client = APIClient(
    base_url="https://test-api.nifa.org.cn",
    org_code="TEST123456",
    timeout=60
)

# 使用自定义客户端
with InfoAPI(client=client) as info_api:
    result = info_api.query_personal_credit(
        id_card="110101199001011234",
        name="张三"
    )
```

## 错误处理

```python
from nifa import InfoAPI
from nifa.exceptions import (
    NifaError,
    NifaValidationError,
    NifaAPIError,
    NifaNetworkError
)

try:
    with InfoAPI() as info_api:
        result = info_api.query_personal_credit(
            id_card="invalid_id",
            name="张三"
        )
except NifaValidationError as e:
    print(f"参数验证错误: {e}")
    print(f"错误字段: {e.details.get('field')}")
except NifaAPIError as e:
    print(f"API调用错误: {e}")
    print(f"状态码: {e.status_code}")
except NifaNetworkError as e:
    print(f"网络错误: {e}")
except NifaError as e:
    print(f"通用错误: {e}")
    print(f"错误代码: {e.error_code}")
```

## 开发

### 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd nifa-client

# 安装依赖
uv sync

# 安装开发依赖
uv sync --dev
```

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行测试并生成覆盖率报告
uv run pytest --cov=nifa --cov-report=html

# 运行特定测试
uv run pytest tests/test_api/test_info_api.py
```

### 代码格式化

```bash
# 格式化代码
uv run black nifa tests examples

# 排序导入
uv run isort nifa tests examples

# 代码检查
uv run flake8 nifa tests examples

# 类型检查
uv run mypy nifa
```

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0

- 🎉 首次发布
- ✅ 支持所有主要API接口
- 🔒 集成国密算法支持
- 📝 完整的文档和示例
- 🧪 全面的测试覆盖

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 [GitHub Issue](https://github.com/your-repo/nifa-client/issues)
- 发送邮件至：<<EMAIL>>