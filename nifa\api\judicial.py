"""
司法数据查询API
实现司法相关数据查询功能
"""

import logging
from typing import Dict, Any, Optional, List

from .client import APIClient
from ..utils.validators import validate_id_card, validate_name, validate_required_fields
from ..exceptions.base import NifaValidationError

logger = logging.getLogger(__name__)


class JudicialAPI:
    """司法数据查询API类"""
    
    def __init__(self, client: Optional[APIClient] = None):
        """
        初始化司法数据查询API
        
        Args:
            client: API客户端实例，如果不提供则创建新实例
        """
        self.client = client or APIClient()
        self._own_client = client is None
    
    def query_court_records(
        self,
        id_card: str,
        name: str,
        query_type: str = "all",
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询法院记录
        
        Args:
            id_card: 身份证号码
            name: 姓名
            query_type: 查询类型（all-全部，civil-民事，criminal-刑事，administrative-行政）
            **kwargs: 其他参数
            
        Returns:
            法院记录查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_id_card(id_card)
        validate_name(name)
        
        if query_type not in ["all", "civil", "criminal", "administrative"]:
            raise NifaValidationError(
                "查询类型必须为all、civil、criminal或administrative",
                field="query_type",
                value=query_type
            )
        
        # 构建请求数据
        request_data = {
            "idCard": id_card,
            "name": name,
            "queryType": query_type,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["idCard", "name"])
        
        logger.info(f"查询法院记录: {name}({id_card[:6]}***{id_card[-4:]}) - {query_type}")
        
        # 发起请求
        response = self.client.post("judicial/court-records", data=request_data)
        
        return response
    
    def query_enforcement_records(
        self,
        id_card: str,
        name: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询执行记录
        
        Args:
            id_card: 身份证号码
            name: 姓名
            **kwargs: 其他参数
            
        Returns:
            执行记录查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_id_card(id_card)
        validate_name(name)
        
        # 构建请求数据
        request_data = {
            "idCard": id_card,
            "name": name,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["idCard", "name"])
        
        logger.info(f"查询执行记录: {name}({id_card[:6]}***{id_card[-4:]})")
        
        # 发起请求
        response = self.client.post("judicial/enforcement-records", data=request_data)
        
        return response
    
    def query_dishonest_records(
        self,
        id_card: str,
        name: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询失信记录
        
        Args:
            id_card: 身份证号码
            name: 姓名
            **kwargs: 其他参数
            
        Returns:
            失信记录查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_id_card(id_card)
        validate_name(name)
        
        # 构建请求数据
        request_data = {
            "idCard": id_card,
            "name": name,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["idCard", "name"])
        
        logger.info(f"查询失信记录: {name}({id_card[:6]}***{id_card[-4:]})")
        
        # 发起请求
        response = self.client.post("judicial/dishonest-records", data=request_data)
        
        return response
    
    def query_restricted_records(
        self,
        id_card: str,
        name: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询限制高消费记录
        
        Args:
            id_card: 身份证号码
            name: 姓名
            **kwargs: 其他参数
            
        Returns:
            限制高消费记录查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_id_card(id_card)
        validate_name(name)
        
        # 构建请求数据
        request_data = {
            "idCard": id_card,
            "name": name,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["idCard", "name"])
        
        logger.info(f"查询限制高消费记录: {name}({id_card[:6]}***{id_card[-4:]})")
        
        # 发起请求
        response = self.client.post("judicial/restricted-records", data=request_data)
        
        return response
    
    def query_enterprise_judicial_records(
        self,
        org_code: str,
        org_name: str,
        query_type: str = "all",
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询企业司法记录
        
        Args:
            org_code: 企业统一社会信用代码或组织机构代码
            org_name: 企业名称
            query_type: 查询类型（all-全部，court-法院记录，enforcement-执行记录）
            **kwargs: 其他参数
            
        Returns:
            企业司法记录查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not org_code or not org_code.strip():
            raise NifaValidationError("企业代码不能为空", field="org_code", value=org_code)
        
        if not org_name or not org_name.strip():
            raise NifaValidationError("企业名称不能为空", field="org_name", value=org_name)
        
        if query_type not in ["all", "court", "enforcement"]:
            raise NifaValidationError(
                "查询类型必须为all、court或enforcement",
                field="query_type",
                value=query_type
            )
        
        # 构建请求数据
        request_data = {
            "orgCode": org_code,
            "orgName": org_name,
            "queryType": query_type,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["orgCode", "orgName"])
        
        logger.info(f"查询企业司法记录: {org_name}({org_code}) - {query_type}")
        
        # 发起请求
        response = self.client.post("judicial/enterprise-records", data=request_data)
        
        return response
    
    def batch_query_judicial_records(
        self,
        query_list: List[Dict[str, str]],
        query_type: str = "all",
        **kwargs
    ) -> Dict[str, Any]:
        """
        批量查询司法记录
        
        Args:
            query_list: 查询列表，每个元素包含idCard和name字段
            query_type: 查询类型
            **kwargs: 其他参数
            
        Returns:
            批量查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not query_list or not isinstance(query_list, list):
            raise NifaValidationError("查询列表不能为空且必须为列表", field="query_list", value=query_list)
        
        if len(query_list) > 50:  # 假设批量查询限制为50条
            raise NifaValidationError(
                "批量查询数量不能超过50条",
                field="query_list",
                value=len(query_list)
            )
        
        # 验证每个查询项
        for i, item in enumerate(query_list):
            if not isinstance(item, dict):
                raise NifaValidationError(
                    f"查询列表第{i+1}项必须为字典",
                    field=f"query_list[{i}]",
                    value=item
                )
            
            validate_required_fields(item, ["idCard", "name"])
            validate_id_card(item["idCard"])
            validate_name(item["name"])
        
        if query_type not in ["all", "court", "enforcement", "dishonest", "restricted"]:
            raise NifaValidationError(
                "查询类型必须为all、court、enforcement、dishonest或restricted",
                field="query_type",
                value=query_type
            )
        
        # 构建请求数据
        request_data = {
            "queryList": query_list,
            "queryType": query_type,
            **kwargs
        }
        
        logger.info(f"批量查询司法记录: {len(query_list)}条记录 - {query_type}")
        
        # 发起请求
        response = self.client.post("judicial/batch-query", data=request_data)
        
        return response
    
    def get_judicial_summary(
        self,
        id_card: str,
        name: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取司法信息汇总
        
        Args:
            id_card: 身份证号码
            name: 姓名
            **kwargs: 其他参数
            
        Returns:
            司法信息汇总
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_id_card(id_card)
        validate_name(name)
        
        # 构建请求数据
        request_data = {
            "idCard": id_card,
            "name": name,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["idCard", "name"])
        
        logger.info(f"获取司法信息汇总: {name}({id_card[:6]}***{id_card[-4:]})")
        
        # 发起请求
        response = self.client.post("judicial/summary", data=request_data)
        
        return response
    
    def close(self) -> None:
        """关闭API客户端"""
        if self._own_client and self.client:
            self.client.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
