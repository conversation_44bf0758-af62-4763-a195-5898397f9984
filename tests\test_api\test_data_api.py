"""
数据上报API测试
测试数据上报和文件上传功能
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch

from nifa.api.data import DataAPI
from nifa.exceptions.base import NifaValidationError


class TestDataAPI:
    """数据上报API测试类"""
    
    def test_init_with_client(self, mock_api_client):
        """测试使用提供的客户端初始化"""
        api = DataAPI(client=mock_api_client)
        assert api.client == mock_api_client
        assert api._own_client is False
    
    def test_init_without_client(self):
        """测试不提供客户端时自动创建"""
        api = DataAPI()
        assert api.client is not None
        assert api._own_client is True
    
    def test_upload_data_success(self, mock_api_client, mock_response_success):
        """测试数据上报成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        result = api.upload_data(
            data_type="loan_info",
            data_content={"amount": 10000, "term": 12}
        )
        
        assert result["code"] == "0000"
        mock_api_client.post.assert_called_once()
        
        # 验证请求数据
        call_args = mock_api_client.post.call_args
        request_data = call_args[1]["data"]
        assert request_data["dataType"] == "loan_info"
        assert "dataContent" in request_data
        assert request_data["encrypted"] is False
    
    def test_upload_data_with_encryption(self, mock_api_client, mock_response_success):
        """测试加密数据上报"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        
        # 模拟没有配置SM4密钥的情况
        with patch('nifa.api.data.settings') as mock_settings:
            mock_settings.NIFA_SM4_KEY = None
            
            result = api.upload_data(
                data_type="loan_info",
                data_content={"amount": 10000},
                encrypt=True
            )
            
            assert result["code"] == "0000"
    
    def test_upload_data_with_batch_no(self, mock_api_client, mock_response_success):
        """测试带批次号的数据上报"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        result = api.upload_data(
            data_type="customer_info",
            data_content={"name": "张三"},
            batch_no="BATCH001"
        )
        
        assert result["code"] == "0000"
        
        # 验证批次号
        call_args = mock_api_client.post.call_args
        request_data = call_args[1]["data"]
        assert request_data["batchNo"] == "BATCH001"
    
    def test_upload_data_invalid_type(self, mock_api_client):
        """测试无效数据类型"""
        api = DataAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.upload_data(
                data_type="",
                data_content={"test": "data"}
            )
        assert "数据类型不能为空" in str(exc_info.value)
    
    def test_upload_data_empty_content(self, mock_api_client):
        """测试空数据内容"""
        api = DataAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.upload_data(
                data_type="test_type",
                data_content=None
            )
        assert "数据内容不能为空" in str(exc_info.value)
    
    def test_upload_data_dict_content(self, mock_api_client, mock_response_success):
        """测试字典类型数据内容"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        test_data = {"key1": "value1", "key2": "value2"}
        
        result = api.upload_data(
            data_type="test_type",
            data_content=test_data
        )
        
        assert result["code"] == "0000"
        
        # 验证数据被JSON序列化
        call_args = mock_api_client.post.call_args
        request_data = call_args[1]["data"]
        assert '"key1": "value1"' in request_data["dataContent"]
    
    def test_upload_data_list_content(self, mock_api_client, mock_response_success):
        """测试列表类型数据内容"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        test_data = [{"id": 1}, {"id": 2}]
        
        result = api.upload_data(
            data_type="test_type",
            data_content=test_data
        )
        
        assert result["code"] == "0000"
    
    def test_upload_file_success(self, mock_api_client, mock_response_success, temp_file):
        """测试文件上传成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        result = api.upload_file(
            file_path=temp_file,
            file_type="txt"
        )
        
        assert result["code"] == "0000"
        mock_api_client.post.assert_called_once()
        
        # 验证请求数据
        call_args = mock_api_client.post.call_args
        request_data = call_args[1]["data"]
        assert "fileName" in request_data
        assert request_data["fileType"] == "txt"
        assert "fileContent" in request_data
        assert "fileSize" in request_data
        assert request_data["encrypted"] is False
    
    def test_upload_file_not_exists(self, mock_api_client):
        """测试上传不存在的文件"""
        api = DataAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.upload_file(
                file_path="/nonexistent/file.txt",
                file_type="txt"
            )
        assert "文件不存在" in str(exc_info.value)
    
    def test_upload_file_empty_path(self, mock_api_client):
        """测试空文件路径"""
        api = DataAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.upload_file(
                file_path="",
                file_type="txt"
            )
        assert "文件路径不能为空" in str(exc_info.value)
    
    def test_upload_file_empty_type(self, mock_api_client, temp_file):
        """测试空文件类型"""
        api = DataAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.upload_file(
                file_path=temp_file,
                file_type=""
            )
        assert "文件类型不能为空" in str(exc_info.value)
    
    def test_upload_file_with_encryption(self, mock_api_client, mock_response_success, temp_file):
        """测试加密文件上传"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        
        # 模拟没有配置SM4密钥的情况
        with patch('nifa.api.data.settings') as mock_settings:
            mock_settings.NIFA_SM4_KEY = None
            
            result = api.upload_file(
                file_path=temp_file,
                file_type="txt",
                encrypt=True
            )
            
            assert result["code"] == "0000"
    
    def test_batch_upload_data_success(self, mock_api_client, mock_response_success):
        """测试批量数据上报成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = DataAPI(client=mock_api_client)
        data_list = [
            {"dataType": "type1", "dataContent": {"key1": "value1"}},
            {"dataType": "type2", "dataContent": {"key2": "value2"}}
        ]
        
        result = api.batch_upload_data(data_list=data_list)
        
        assert result["code"] == "0000"
        mock_api_client.post.assert_called_once()
    
    def test_batch_upload_data_empty_list(self, mock_api_client):
        """测试空数据列表"""
        api = DataAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.batch_upload_data(data_list=[])
        assert "数据列表不能为空" in str(exc_info.value)
    
    def test_batch_upload_data_too_many_items(self, mock_api_client):
        """测试数据列表项目过多"""
        api = DataAPI(client=mock_api_client)
        
        # 创建超过限制的数据列表
        data_list = [{"dataType": f"type{i}", "dataContent": {}} for i in range(101)]
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.batch_upload_data(data_list=data_list)
        assert "批量上报数据不能超过100条" in str(exc_info.value)
    
    def test_batch_upload_data_invalid_item(self, mock_api_client):
        """测试无效数据项"""
        api = DataAPI(client=mock_api_client)
        
        data_list = [
            {"dataType": "type1", "dataContent": {"key1": "value1"}},
            "invalid_item"  # 无效项
        ]
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.batch_upload_data(data_list=data_list)
        assert "数据列表第2项必须为字典" in str(exc_info.value)
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with DataAPI() as api:
            assert isinstance(api, DataAPI)
            assert api.client is not None
    
    def test_close_own_client(self):
        """测试关闭自有客户端"""
        api = DataAPI()
        client = api.client
        
        api.close()
        
        # 验证客户端被关闭
        assert api.client is None
    
    def test_close_external_client(self, mock_api_client):
        """测试不关闭外部客户端"""
        api = DataAPI(client=mock_api_client)
        
        api.close()
        
        # 外部客户端不应该被关闭
        assert api.client == mock_api_client
