"""
完整使用示例
演示NIFA Credit API Client的所有主要功能
"""

import logging
import os
import tempfile
from datetime import datetime, timedelta

# 导入所有API类
from nifa import (
    InfoAPI, DataAPI, TaskAPI, QueryCountAPI, JudicialAPI,
    NifaError, Settings
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def setup_environment():
    """设置环境变量"""
    # 在实际使用中，这些应该在.env文件中配置
    os.environ["NIFA_ORG_CODE"] = "YOUR_ORG_CODE"
    os.environ["NIFA_BASE_URL"] = "https://api.nifa.org.cn"
    os.environ["ENVIRONMENT"] = "development"
    os.environ["DEBUG"] = "true"
    
    # 可选的加密配置
    # os.environ["NIFA_SM2_PUBLIC_KEY"] = "your_sm2_public_key"
    # os.environ["NIFA_SM2_PRIVATE_KEY"] = "your_sm2_private_key"
    # os.environ["NIFA_SM4_KEY"] = "your_32_char_hex_sm4_key"


def demo_info_api():
    """演示信用信息查询API"""
    logger.info("=== 信用信息查询API演示 ===")
    
    try:
        with InfoAPI() as info_api:
            # 1. 个人信用查询
            logger.info("1. 个人信用查询")
            personal_result = info_api.query_personal_credit(
                id_card="110101199001010019",
                name="张三",
                query_type="01",
                query_reason="贷款申请"
            )
            logger.info(f"个人信用查询结果: {personal_result}")
            
            # 2. 企业信用查询
            logger.info("2. 企业信用查询")
            enterprise_result = info_api.query_enterprise_credit(
                org_code="91110000123456789X",
                org_name="示例企业有限公司",
                query_type="01"
            )
            logger.info(f"企业信用查询结果: {enterprise_result}")
            
            # 3. 批量查询
            logger.info("3. 批量个人信用查询")
            query_list = [
                {"idCard": "110101199001010019", "name": "张三"},
                {"idCard": "110101199001010020", "name": "李四"}
            ]
            batch_result = info_api.batch_query_personal_credit(
                query_list=query_list,
                query_type="01"
            )
            logger.info(f"批量查询结果: {batch_result}")
            
            # 4. 查询历史
            logger.info("4. 查询历史记录")
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            
            history_result = info_api.get_query_history(
                start_date=start_date,
                end_date=end_date,
                page=1,
                page_size=10
            )
            logger.info(f"查询历史: {history_result}")
            
    except NifaError as e:
        logger.error(f"信用信息查询失败: {e}")


def demo_data_api():
    """演示数据上报API"""
    logger.info("=== 数据上报API演示 ===")
    
    try:
        with DataAPI() as data_api:
            # 1. 单条数据上报
            logger.info("1. 单条数据上报")
            single_data = {
                "customerName": "张三",
                "idCard": "110101199001010019",
                "loanAmount": "100000.00",
                "loanDate": "2023-01-01",
                "status": "正常"
            }
            
            upload_result = data_api.upload_data(
                data_type="loan_info",
                data_content=single_data,
                batch_no=f"BATCH{datetime.now().strftime('%Y%m%d%H%M%S')}"
            )
            logger.info(f"数据上报结果: {upload_result}")
            
            # 2. 批量数据上报
            logger.info("2. 批量数据上报")
            batch_data = [
                {
                    "dataType": "customer_info",
                    "dataContent": {
                        "customerName": "李四",
                        "idCard": "110101199001010020",
                        "phone": "13812345678"
                    }
                },
                {
                    "dataType": "loan_info", 
                    "dataContent": {
                        "customerName": "李四",
                        "idCard": "110101199001010020",
                        "loanAmount": "200000.00",
                        "loanDate": "2023-01-02"
                    }
                }
            ]
            
            batch_result = data_api.batch_upload_data(
                data_list=batch_data,
                batch_no=f"BATCH{datetime.now().strftime('%Y%m%d%H%M%S')}"
            )
            logger.info(f"批量上报结果: {batch_result}")
            
            # 3. 文件上传
            logger.info("3. 文件上传")
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
                temp_file.write("姓名,身份证号,贷款金额\n")
                temp_file.write("王五,110101199001010021,150000.00\n")
                temp_file_path = temp_file.name
            
            try:
                file_result = data_api.upload_file(
                    file_path=temp_file_path,
                    file_type="csv",
                    description="测试数据文件"
                )
                logger.info(f"文件上传结果: {file_result}")
            finally:
                os.unlink(temp_file_path)
                
    except NifaError as e:
        logger.error(f"数据上报失败: {e}")


def demo_judicial_api():
    """演示司法数据查询API"""
    logger.info("=== 司法数据查询API演示 ===")
    
    try:
        with JudicialAPI() as judicial_api:
            # 1. 法院记录查询
            logger.info("1. 法院记录查询")
            court_result = judicial_api.query_court_records(
                id_card="110101199001010019",
                name="张三",
                query_type="all"
            )
            logger.info(f"法院记录: {court_result}")
            
            # 2. 失信记录查询
            logger.info("2. 失信记录查询")
            dishonest_result = judicial_api.query_dishonest_records(
                id_card="110101199001010019",
                name="张三"
            )
            logger.info(f"失信记录: {dishonest_result}")
            
            # 3. 司法信息汇总
            logger.info("3. 司法信息汇总")
            summary_result = judicial_api.get_judicial_summary(
                id_card="110101199001010019",
                name="张三"
            )
            logger.info(f"司法信息汇总: {summary_result}")
            
    except NifaError as e:
        logger.error(f"司法数据查询失败: {e}")


def demo_query_count_api():
    """演示查询量统计API"""
    logger.info("=== 查询量统计API演示 ===")
    
    try:
        with QueryCountAPI() as query_api:
            # 1. 查询量统计
            logger.info("1. 查询量统计")
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            
            count_result = query_api.get_query_count(
                start_date=start_date,
                end_date=end_date
            )
            logger.info(f"查询量统计: {count_result}")
            
            # 2. 剩余配额
            logger.info("2. 剩余配额查询")
            quota_result = query_api.get_remaining_quota(quota_type="monthly")
            logger.info(f"剩余配额: {quota_result}")
            
            # 3. 费用汇总
            logger.info("3. 费用汇总")
            cost_result = query_api.get_cost_summary(
                start_date=start_date,
                end_date=end_date,
                cost_type="total"
            )
            logger.info(f"费用汇总: {cost_result}")
            
    except NifaError as e:
        logger.error(f"查询量统计失败: {e}")


def demo_task_api():
    """演示报送状态查询API"""
    logger.info("=== 报送状态查询API演示 ===")
    
    try:
        with TaskAPI() as task_api:
            # 1. 任务列表
            logger.info("1. 报送任务列表")
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            
            task_list = task_api.get_task_list(
                start_date=start_date,
                end_date=end_date,
                page=1,
                page_size=10
            )
            logger.info(f"任务列表: {task_list}")
            
            # 2. 任务统计
            logger.info("2. 任务统计")
            stats_result = task_api.get_task_statistics(
                start_date=start_date,
                end_date=end_date,
                group_by="status"
            )
            logger.info(f"任务统计: {stats_result}")
            
    except NifaError as e:
        logger.error(f"报送状态查询失败: {e}")


def demo_custom_configuration():
    """演示自定义配置"""
    logger.info("=== 自定义配置演示 ===")
    
    try:
        # 创建自定义配置
        custom_settings = Settings(
            NIFA_ORG_CODE="CUSTOM_ORG_CODE",
            NIFA_BASE_URL="https://test-api.nifa.org.cn",
            NIFA_TIMEOUT=60,
            NIFA_MAX_RETRIES=5,
            ENVIRONMENT="testing"
        )
        
        logger.info(f"自定义配置: {custom_settings.NIFA_BASE_URL}")
        logger.info(f"环境: {custom_settings.ENVIRONMENT}")
        logger.info(f"超时时间: {custom_settings.NIFA_TIMEOUT}秒")
        
        # 使用自定义配置的API客户端
        from nifa.api.client import APIClient
        
        custom_client = APIClient(
            base_url=custom_settings.NIFA_BASE_URL,
            org_code=custom_settings.NIFA_ORG_CODE,
            timeout=custom_settings.NIFA_TIMEOUT
        )
        
        with InfoAPI(client=custom_client) as info_api:
            logger.info("使用自定义配置的API客户端创建成功")
            
    except Exception as e:
        logger.error(f"自定义配置演示失败: {e}")


def main():
    """主函数"""
    logger.info("开始NIFA Credit API Client完整功能演示")
    
    # 设置环境
    setup_environment()
    
    try:
        # 演示各个API
        demo_info_api()
        demo_data_api()
        demo_judicial_api()
        demo_query_count_api()
        demo_task_api()
        demo_custom_configuration()
        
        logger.info("所有API演示完成")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
    
    logger.info("NIFA Credit API Client演示结束")


if __name__ == "__main__":
    main()
