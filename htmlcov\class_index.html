<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">55%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-03 16:50 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_9589fface2250470___init___py.html">nifa\__init__.py</a></td>
                <td class="name left"><a href="z_9589fface2250470___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838___init___py.html">nifa\api\__init__.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t34">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t34"><data value='APIClient'>APIClient</data></a></td>
                <td>70</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="64 70">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t19">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t19"><data value='DataAPI'>DataAPI</data></a></td>
                <td>102</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="60 102">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t16">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t16"><data value='InfoAPI'>InfoAPI</data></a></td>
                <td>64</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="59 64">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t16">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t16"><data value='JudicialAPI'>JudicialAPI</data></a></td>
                <td>70</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="0 70">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t16">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t16"><data value='QueryCountAPI'>QueryCountAPI</data></a></td>
                <td>57</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="0 57">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t16">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t16"><data value='TaskAPI'>TaskAPI</data></a></td>
                <td>65</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="33 65">51%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2___init___py.html">nifa\auth\__init__.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t27">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t27"><data value='SM2Encryption'>SM2Encryption</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t175">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t175"><data value='SM4Encryption'>SM4Encryption</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="22 30">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t71">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t71"><data value='SignatureBuilder'>SignatureBuilder</data></a></td>
                <td>32</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="23 32">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t254">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t254"><data value='RequestSigner'>RequestSigner</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="29 58">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595___init___py.html">nifa\config\__init__.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t16">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t16"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t25">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t25"><data value='SignatureAlgorithm'>SignatureAlgorithm</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t31">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t31"><data value='Environment'>Environment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t39">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t39"><data value='CacheConfig'>CacheConfig</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t54">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t54"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t63">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t63"><data value='SecurityConfig'>SecurityConfig</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t91">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t91"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t105">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t105"><data value='EnhancedSettings'>EnhancedSettings</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t130">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t130"><data value='Config'>EnhancedSettings.Config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>86</td>
                <td>86</td>
                <td>0</td>
                <td class="right" data-ratio="0 86">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t15">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t15"><data value='Environment'>Environment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t23">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t23"><data value='Settings'>Settings</data></a></td>
                <td>39</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="38 39">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t194">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t194"><data value='Config'>Settings.Config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="63 63">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5___init___py.html">nifa\core\__init__.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t16">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t16"><data value='CircuitState'>CircuitState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t24">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t24"><data value='CircuitBreakerConfig'>CircuitBreakerConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t33">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t33"><data value='CircuitBreakerError'>CircuitBreakerError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t38">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t38"><data value='CircuitBreaker'>CircuitBreaker</data></a></td>
                <td>52</td>
                <td>52</td>
                <td>0</td>
                <td class="right" data-ratio="0 52">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t137">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t137"><data value='CircuitBreakerManager'>CircuitBreakerManager</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440___init___py.html">nifa\exceptions\__init__.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t10">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t10"><data value='NifaError'>NifaError</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t38">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t38"><data value='NifaAPIError'>NifaAPIError</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t53">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t53"><data value='NifaResponseError'>NifaResponseError</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t75">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t75"><data value='NifaSignatureError'>NifaSignatureError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t80">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t80"><data value='NifaEncryptionError'>NifaEncryptionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t85">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t85"><data value='NifaValidationError'>NifaValidationError</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t101">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t101"><data value='NifaTimeoutError'>NifaTimeoutError</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t115">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t115"><data value='NifaNetworkError'>NifaNetworkError</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t129">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t129"><data value='NifaConfigurationError'>NifaConfigurationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t134">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t134"><data value='NifaAuthenticationError'>NifaAuthenticationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t139">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t139"><data value='NifaRateLimitError'>NifaRateLimitError</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t146">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t146"><data value='NifaBusinessError'>NifaBusinessError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t151">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t151"><data value='NifaDataError'>NifaDataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t156">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t156"><data value='NifaCircuitBreakerError'>NifaCircuitBreakerError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="39 42">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76___init___py.html">nifa\models\__init__.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t14">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t14"><data value='BaseRequest'>BaseRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t21">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t21"><data value='Config'>BaseRequest.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t31">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t31"><data value='BaseResponse'>BaseResponse</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t39">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t39"><data value='Config'>BaseResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t59">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t59"><data value='PaginatedData'>PaginatedData</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t69">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t69"><data value='Config'>PaginatedData.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t97">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t97"><data value='PaginatedResponse'>PaginatedResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t102">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t102"><data value='ErrorDetail'>ErrorDetail</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t110">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t110"><data value='ValidationErrorResponse'>ValidationErrorResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t115">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t115"><data value='HealthCheckResponse'>HealthCheckResponse</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe___init___py.html">nifa\utils\__init__.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>102</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="99 102">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>78</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="75 78">96%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1436</td>
                <td>652</td>
                <td>0</td>
                <td class="right" data-ratio="784 1436">55%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-03 16:50 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
