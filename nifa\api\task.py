"""
报送状态查询API
实现报送任务状态查询功能
"""

import logging
from typing import Dict, Any, Optional, List

from .client import APIClient
from ..utils.validators import validate_required_fields, validate_date_string
from ..exceptions.base import NifaValidationError

logger = logging.getLogger(__name__)


class TaskAPI:
    """报送状态查询API类"""
    
    def __init__(self, client: Optional[APIClient] = None):
        """
        初始化报送状态查询API
        
        Args:
            client: API客户端实例，如果不提供则创建新实例
        """
        self.client = client or APIClient()
        self._own_client = client is None
    
    def query_task_status(
        self,
        task_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询报送任务状态
        
        Args:
            task_id: 任务ID
            **kwargs: 其他参数
            
        Returns:
            任务状态信息
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not task_id or not task_id.strip():
            raise NifaValidationError("任务ID不能为空", field="task_id", value=task_id)
        
        # 构建请求数据
        request_data = {
            "taskId": task_id,
            **kwargs
        }
        
        logger.info(f"查询报送任务状态: {task_id}")
        
        # 发起请求
        response = self.client.post("task/status", data=request_data)
        
        return response
    
    def query_batch_task_status(
        self,
        task_ids: List[str],
        **kwargs
    ) -> Dict[str, Any]:
        """
        批量查询报送任务状态
        
        Args:
            task_ids: 任务ID列表
            **kwargs: 其他参数
            
        Returns:
            批量任务状态信息
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not task_ids or not isinstance(task_ids, list):
            raise NifaValidationError("任务ID列表不能为空且必须为列表", field="task_ids", value=task_ids)
        
        if len(task_ids) > 100:  # 假设批量查询限制为100个
            raise NifaValidationError(
                "批量查询任务数量不能超过100个",
                field="task_ids",
                value=len(task_ids)
            )
        
        # 验证每个任务ID
        for i, task_id in enumerate(task_ids):
            if not task_id or not str(task_id).strip():
                raise NifaValidationError(
                    f"任务ID列表第{i+1}项不能为空",
                    field=f"task_ids[{i}]",
                    value=task_id
                )
        
        # 构建请求数据
        request_data = {
            "taskIds": task_ids,
            **kwargs
        }
        
        logger.info(f"批量查询报送任务状态: {len(task_ids)}个任务")
        
        # 发起请求
        response = self.client.post("task/batch-status", data=request_data)
        
        return response
    
    def get_task_list(
        self,
        start_date: str,
        end_date: str,
        status: Optional[str] = None,
        task_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取报送任务列表
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            status: 任务状态（可选）
            task_type: 任务类型（可选）
            page: 页码
            page_size: 每页大小
            **kwargs: 其他参数
            
        Returns:
            任务列表
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        if page < 1:
            raise NifaValidationError("页码必须大于0", field="page", value=page)
        
        if page_size < 1 or page_size > 100:
            raise NifaValidationError(
                "每页大小必须在1-100之间",
                field="page_size",
                value=page_size
            )
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            "page": page,
            "pageSize": page_size,
            **kwargs
        }
        
        if status:
            request_data["status"] = status
        
        if task_type:
            request_data["taskType"] = task_type
        
        logger.info(f"获取报送任务列表: {start_date} 至 {end_date}")
        
        # 发起请求
        response = self.client.post("task/list", data=request_data)
        
        return response
    
    def get_task_detail(
        self,
        task_id: str,
        include_logs: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取报送任务详情
        
        Args:
            task_id: 任务ID
            include_logs: 是否包含日志信息
            **kwargs: 其他参数
            
        Returns:
            任务详情
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not task_id or not task_id.strip():
            raise NifaValidationError("任务ID不能为空", field="task_id", value=task_id)
        
        # 构建请求数据
        request_data = {
            "taskId": task_id,
            "includeLogs": include_logs,
            **kwargs
        }
        
        logger.info(f"获取报送任务详情: {task_id}")
        
        # 发起请求
        response = self.client.post("task/detail", data=request_data)
        
        return response
    
    def retry_failed_task(
        self,
        task_id: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        重试失败的报送任务
        
        Args:
            task_id: 任务ID
            **kwargs: 其他参数
            
        Returns:
            重试结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not task_id or not task_id.strip():
            raise NifaValidationError("任务ID不能为空", field="task_id", value=task_id)
        
        # 构建请求数据
        request_data = {
            "taskId": task_id,
            **kwargs
        }
        
        logger.info(f"重试失败的报送任务: {task_id}")
        
        # 发起请求
        response = self.client.post("task/retry", data=request_data)
        
        return response
    
    def cancel_task(
        self,
        task_id: str,
        reason: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        取消报送任务
        
        Args:
            task_id: 任务ID
            reason: 取消原因
            **kwargs: 其他参数
            
        Returns:
            取消结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not task_id or not task_id.strip():
            raise NifaValidationError("任务ID不能为空", field="task_id", value=task_id)
        
        # 构建请求数据
        request_data = {
            "taskId": task_id,
            **kwargs
        }
        
        if reason:
            request_data["reason"] = reason
        
        logger.info(f"取消报送任务: {task_id}")
        
        # 发起请求
        response = self.client.post("task/cancel", data=request_data)
        
        return response
    
    def get_task_statistics(
        self,
        start_date: str,
        end_date: str,
        group_by: str = "status",
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取报送任务统计信息
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            group_by: 分组方式（status-按状态，type-按类型，date-按日期）
            **kwargs: 其他参数
            
        Returns:
            任务统计信息
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        if group_by not in ["status", "type", "date"]:
            raise NifaValidationError(
                "分组方式必须为status、type或date",
                field="group_by",
                value=group_by
            )
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            "groupBy": group_by,
            **kwargs
        }
        
        logger.info(f"获取报送任务统计信息: {start_date} 至 {end_date} (按{group_by}分组)")
        
        # 发起请求
        response = self.client.post("task/statistics", data=request_data)
        
        return response
    
    def close(self) -> None:
        """关闭API客户端"""
        if self._own_client and self.client:
            self.client.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
