"""
查询量耗用查询API
实现查询量统计和耗用查询功能
"""

import logging
from typing import Dict, Any, Optional

from .client import APIClient
from ..utils.validators import validate_required_fields, validate_date_string
from ..exceptions.base import NifaValidationError

logger = logging.getLogger(__name__)


class QueryCountAPI:
    """查询量耗用查询API类"""
    
    def __init__(self, client: Optional[APIClient] = None):
        """
        初始化查询量耗用查询API
        
        Args:
            client: API客户端实例，如果不提供则创建新实例
        """
        self.client = client or APIClient()
        self._own_client = client is None
    
    def get_query_count(
        self,
        start_date: str,
        end_date: str,
        query_type: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取查询量统计
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            query_type: 查询类型（可选）
            **kwargs: 其他参数
            
        Returns:
            查询量统计结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            **kwargs
        }
        
        if query_type:
            request_data["queryType"] = query_type
        
        # 验证必填字段
        validate_required_fields(request_data, ["startDate", "endDate"])
        
        logger.info(f"获取查询量统计: {start_date} 至 {end_date}")
        
        # 发起请求
        response = self.client.post("query/count", data=request_data)
        
        return response
    
    def get_remaining_quota(
        self,
        quota_type: str = "monthly",
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取剩余查询配额
        
        Args:
            quota_type: 配额类型（daily-日配额，monthly-月配额，yearly-年配额）
            **kwargs: 其他参数
            
        Returns:
            剩余配额信息
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if quota_type not in ["daily", "monthly", "yearly"]:
            raise NifaValidationError(
                "配额类型必须为daily、monthly或yearly",
                field="quota_type",
                value=quota_type
            )
        
        # 构建请求数据
        request_data = {
            "quotaType": quota_type,
            **kwargs
        }
        
        logger.info(f"获取剩余查询配额: {quota_type}")
        
        # 发起请求
        response = self.client.post("query/remaining-quota", data=request_data)
        
        return response
    
    def get_usage_detail(
        self,
        start_date: str,
        end_date: str,
        detail_type: str = "daily",
        page: int = 1,
        page_size: int = 20,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取使用量明细
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            detail_type: 明细类型（daily-按日，hourly-按小时）
            page: 页码
            page_size: 每页大小
            **kwargs: 其他参数
            
        Returns:
            使用量明细
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        if detail_type not in ["daily", "hourly"]:
            raise NifaValidationError(
                "明细类型必须为daily或hourly",
                field="detail_type",
                value=detail_type
            )
        
        if page < 1:
            raise NifaValidationError("页码必须大于0", field="page", value=page)
        
        if page_size < 1 or page_size > 100:
            raise NifaValidationError(
                "每页大小必须在1-100之间",
                field="page_size",
                value=page_size
            )
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            "detailType": detail_type,
            "page": page,
            "pageSize": page_size,
            **kwargs
        }
        
        logger.info(f"获取使用量明细: {start_date} 至 {end_date} ({detail_type})")
        
        # 发起请求
        response = self.client.post("query/usage-detail", data=request_data)
        
        return response
    
    def get_cost_summary(
        self,
        start_date: str,
        end_date: str,
        cost_type: str = "total",
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取费用汇总
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            cost_type: 费用类型（total-总费用，by_service-按服务分类）
            **kwargs: 其他参数
            
        Returns:
            费用汇总信息
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        if cost_type not in ["total", "by_service"]:
            raise NifaValidationError(
                "费用类型必须为total或by_service",
                field="cost_type",
                value=cost_type
            )
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            "costType": cost_type,
            **kwargs
        }
        
        logger.info(f"获取费用汇总: {start_date} 至 {end_date} ({cost_type})")
        
        # 发起请求
        response = self.client.post("query/cost-summary", data=request_data)
        
        return response
    
    def get_service_statistics(
        self,
        start_date: str,
        end_date: str,
        service_type: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            service_type: 服务类型（可选）
            **kwargs: 其他参数
            
        Returns:
            服务统计信息
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            **kwargs
        }
        
        if service_type:
            request_data["serviceType"] = service_type
        
        logger.info(f"获取服务统计信息: {start_date} 至 {end_date}")
        
        # 发起请求
        response = self.client.post("query/service-statistics", data=request_data)
        
        return response
    
    def export_usage_report(
        self,
        start_date: str,
        end_date: str,
        report_format: str = "excel",
        **kwargs
    ) -> Dict[str, Any]:
        """
        导出使用量报告
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            report_format: 报告格式（excel-Excel格式，pdf-PDF格式）
            **kwargs: 其他参数
            
        Returns:
            导出结果（包含下载链接）
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        if report_format not in ["excel", "pdf"]:
            raise NifaValidationError(
                "报告格式必须为excel或pdf",
                field="report_format",
                value=report_format
            )
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            "reportFormat": report_format,
            **kwargs
        }
        
        logger.info(f"导出使用量报告: {start_date} 至 {end_date} ({report_format})")
        
        # 发起请求
        response = self.client.post("query/export-usage-report", data=request_data)
        
        return response
    
    def close(self) -> None:
        """关闭API客户端"""
        if self._own_client and self.client:
            self.client.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
