"""
签名认证模块
实现API请求签名功能
"""

import hashlib
import hmac
import random
import string
import time
from typing import List, Dict, Any, Callable, Optional
from functools import wraps
import logging

from ..config.settings import settings
from ..exceptions.base import NifaSignatureError
from ..utils.helpers import calculate_sha256, calculate_md5

logger = logging.getLogger(__name__)


def generate_random_code(length: int = 16) -> str:
    """
    生成随机数字字符串
    
    Args:
        length: 随机码长度
        
    Returns:
        随机数字字符串
    """
    return ''.join(random.choices(string.digits, k=length))


def sha256_sign(data: str, key: Optional[str] = None) -> str:
    """
    SHA256签名
    
    Args:
        data: 待签名数据
        key: 签名密钥（可选）
        
    Returns:
        签名结果
    """
    if key:
        # HMAC-SHA256
        return hmac.new(
            key.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest().upper()
    else:
        # 普通SHA256
        return calculate_sha256(data).upper()


def md5_sign(data: str) -> str:
    """
    MD5签名
    
    Args:
        data: 待签名数据
        
    Returns:
        签名结果
    """
    return calculate_md5(data).upper()


class SignatureBuilder:
    """签名构建器"""
    
    def __init__(self, algorithm: str = "SHA256"):
        """
        初始化签名构建器
        
        Args:
            algorithm: 签名算法，支持 SHA256, MD5
        """
        self.algorithm = algorithm.upper()
        if self.algorithm not in ["SHA256", "MD5"]:
            raise NifaSignatureError(f"不支持的签名算法: {algorithm}")
    
    def build_signature_string(self, params: Dict[str, Any], exclude_keys: Optional[List[str]] = None) -> str:
        """
        构建签名字符串
        
        Args:
            params: 参数字典
            exclude_keys: 排除的键列表
            
        Returns:
            签名字符串
        """
        exclude_keys = exclude_keys or ['sign', 'signature']
        
        # 过滤并排序参数
        filtered_params = {
            k: v for k, v in params.items()
            if k not in exclude_keys and v is not None and str(v).strip() != ""
        }
        
        # 按键名排序
        sorted_keys = sorted(filtered_params.keys())
        
        # 构建签名字符串
        sign_parts = []
        for key in sorted_keys:
            value = filtered_params[key]
            # 处理不同类型的值
            if isinstance(value, (dict, list)):
                import json
                value_str = json.dumps(value, separators=(',', ':'), ensure_ascii=False)
            else:
                value_str = str(value)
            
            sign_parts.append(f"{key}={value_str}")
        
        signature_string = "&".join(sign_parts)
        logger.debug(f"Signature string: {signature_string}")
        
        return signature_string
    
    def build_signature(self, components: List[str], key: Optional[str] = None) -> str:
        """
        构建签名
        
        Args:
            components: 签名组件列表
            key: 签名密钥（可选）
            
        Returns:
            签名结果
        """
        # 连接所有组件
        sign_string = "".join(str(component) for component in components if component is not None)
        
        logger.debug(f"Sign string: {sign_string}")
        
        # 根据算法生成签名
        if self.algorithm == "SHA256":
            return sha256_sign(sign_string, key)
        elif self.algorithm == "MD5":
            return md5_sign(sign_string)
        else:
            raise NifaSignatureError(f"不支持的签名算法: {self.algorithm}")
    
    def sign_request(self, params: Dict[str, Any], key: Optional[str] = None) -> str:
        """
        对请求参数进行签名
        
        Args:
            params: 请求参数
            key: 签名密钥（可选）
            
        Returns:
            签名结果
        """
        # 构建签名字符串
        signature_string = self.build_signature_string(params)
        
        # 生成签名
        return self.build_signature([signature_string], key)
    
    def verify_signature(
        self,
        params: Dict[str, Any],
        signature: str,
        key: Optional[str] = None
    ) -> bool:
        """
        验证签名
        
        Args:
            params: 请求参数
            signature: 待验证的签名
            key: 签名密钥（可选）
            
        Returns:
            签名是否有效
        """
        try:
            expected_signature = self.sign_request(params, key)
            return expected_signature.upper() == signature.upper()
        except Exception as e:
            logger.error(f"签名验证失败: {str(e)}")
            return False


def with_signature(
    signature_key: Optional[str] = None,
    algorithm: str = "SHA256",
    timestamp_key: str = "timestamp",
    random_key: str = "randomCode",
    signature_key_name: str = "sign"
) -> Callable:
    """
    签名装饰器
    
    Args:
        signature_key: 签名密钥
        algorithm: 签名算法
        timestamp_key: 时间戳参数名
        random_key: 随机码参数名
        signature_key_name: 签名参数名
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取请求参数
            if 'data' in kwargs and isinstance(kwargs['data'], dict):
                request_data = kwargs['data'].copy()
            elif len(args) > 1 and isinstance(args[1], dict):
                request_data = args[1].copy()
            else:
                request_data = {}
            
            # 添加时间戳和随机码
            if timestamp_key not in request_data:
                request_data[timestamp_key] = str(int(time.time() * 1000))
            
            if random_key not in request_data:
                request_data[random_key] = generate_random_code()
            
            # 添加机构代码
            if 'orgCode' not in request_data:
                request_data['orgCode'] = settings.NIFA_ORG_CODE
            
            # 生成签名
            signature_builder = SignatureBuilder(algorithm)
            signature = signature_builder.sign_request(request_data, signature_key)
            request_data[signature_key_name] = signature
            
            # 更新参数
            if 'data' in kwargs:
                kwargs['data'] = request_data
            elif len(args) > 1:
                args = list(args)
                args[1] = request_data
                args = tuple(args)
            
            logger.debug(f"Request signed with {algorithm}: {signature}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


class RequestSigner:
    """请求签名器"""
    
    def __init__(
        self,
        org_code: str,
        algorithm: str = "SHA256",
        signature_key: Optional[str] = None
    ):
        """
        初始化请求签名器
        
        Args:
            org_code: 机构代码
            algorithm: 签名算法
            signature_key: 签名密钥
        """
        self.org_code = org_code
        self.signature_builder = SignatureBuilder(algorithm)
        self.signature_key = signature_key
    
    def sign_request_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        对请求数据进行签名
        
        Args:
            data: 请求数据
            
        Returns:
            签名后的请求数据
        """
        # 复制数据以避免修改原始数据
        signed_data = data.copy()
        
        # 添加必要字段
        signed_data['orgCode'] = self.org_code
        signed_data['timestamp'] = str(int(time.time() * 1000))
        signed_data['randomCode'] = generate_random_code()
        
        # 生成签名
        signature = self.signature_builder.sign_request(signed_data, self.signature_key)
        signed_data['sign'] = signature
        
        return signed_data
    
    def verify_response_signature(
        self,
        response_data: Dict[str, Any],
        signature: str
    ) -> bool:
        """
        验证响应签名
        
        Args:
            response_data: 响应数据
            signature: 响应签名
            
        Returns:
            签名是否有效
        """
        return self.signature_builder.verify_signature(
            response_data,
            signature,
            self.signature_key
        )
