"""
基础数据模型
定义通用的请求和响应结构
"""

from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field, validator


T = TypeVar('T')


class BaseRequest(BaseModel):
    """基础请求模型"""
    org_code: str = Field(..., description="机构代码")
    timestamp: Optional[str] = Field(default=None, description="时间戳")
    random_code: Optional[str] = Field(default=None, description="随机码")
    sign: Optional[str] = Field(default=None, description="签名")
    
    class Config:
        # 允许字段别名
        allow_population_by_field_name = True
        # 字段别名映射
        fields = {
            'org_code': {'alias': 'orgCode'},
            'random_code': {'alias': 'randomCode'}
        }


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模型"""
    code: str = Field(..., description="响应码")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")
    request_id: Optional[str] = Field(default=None, description="请求ID")
    timestamp: Optional[datetime] = Field(default=None, description="响应时间")
    
    class Config:
        fields = {
            'request_id': {'alias': 'requestId'}
        }
    
    @validator('code')
    def validate_code(cls, v):
        if not v:
            raise ValueError('响应码不能为空')
        return v
    
    def is_success(self) -> bool:
        """判断响应是否成功"""
        return self.code == "0000"
    
    def get_error_message(self) -> str:
        """获取错误消息"""
        return self.message if not self.is_success() else ""


class PaginatedData(BaseModel):
    """分页数据模型"""
    total: int = Field(..., ge=0, description="总记录数")
    page: int = Field(..., ge=1, description="当前页码")
    page_size: int = Field(..., ge=1, le=1000, description="每页大小")
    pages: int = Field(..., ge=0, description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
    items: List[Any] = Field(default_factory=list, description="数据项列表")
    
    class Config:
        fields = {
            'page_size': {'alias': 'pageSize'},
            'has_next': {'alias': 'hasNext'},
            'has_prev': {'alias': 'hasPrev'}
        }
    
    @validator('pages', always=True)
    def calculate_pages(cls, v, values):
        """计算总页数"""
        total = values.get('total', 0)
        page_size = values.get('page_size', 1)
        return (total + page_size - 1) // page_size if total > 0 else 0
    
    @validator('has_next', always=True)
    def calculate_has_next(cls, v, values):
        """计算是否有下一页"""
        page = values.get('page', 1)
        pages = values.get('pages', 0)
        return page < pages
    
    @validator('has_prev', always=True)
    def calculate_has_prev(cls, v, values):
        """计算是否有上一页"""
        page = values.get('page', 1)
        return page > 1


class PaginatedResponse(BaseResponse[PaginatedData]):
    """分页响应模型"""
    pass


class ErrorDetail(BaseModel):
    """错误详情模型"""
    field: Optional[str] = Field(default=None, description="错误字段")
    code: Optional[str] = Field(default=None, description="错误代码")
    message: str = Field(..., description="错误消息")
    value: Optional[Any] = Field(default=None, description="错误值")


class ValidationErrorResponse(BaseResponse[List[ErrorDetail]]):
    """验证错误响应模型"""
    pass


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="版本号")
    uptime: float = Field(..., description="运行时间（秒）")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="依赖服务状态")
    
    def is_healthy(self) -> bool:
        """判断服务是否健康"""
        return self.status.lower() == "healthy"