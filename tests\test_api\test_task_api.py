"""
任务状态查询API测试
测试报送状态查询功能
"""

import pytest
from unittest.mock import Mock

from nifa.api.task import TaskAPI
from nifa.exceptions.base import NifaValidationError


class TestTaskAPI:
    """任务状态查询API测试类"""
    
    def test_init_with_client(self, mock_api_client):
        """测试使用提供的客户端初始化"""
        api = TaskAPI(client=mock_api_client)
        assert api.client == mock_api_client
        assert api._own_client is False
    
    def test_init_without_client(self):
        """测试不提供客户端时自动创建"""
        api = TaskAPI()
        assert api.client is not None
        assert api._own_client is True
    
    def test_query_task_status_success(self, mock_api_client, mock_response_success):
        """测试查询任务状态成功"""
        mock_api_client.get.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.query_task_status(task_id="TASK123456789")
        
        assert result["code"] == "0000"
        mock_api_client.get.assert_called_once()
        
        # 验证请求参数
        call_args = mock_api_client.get.call_args
        assert "task/status" in call_args[0][0]
        assert call_args[1]["params"]["taskId"] == "TASK123456789"
    
    def test_query_task_status_empty_id(self, mock_api_client):
        """测试查询空任务ID"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_task_status(task_id="")
        assert "任务ID不能为空" in str(exc_info.value)
    
    def test_query_task_status_none_id(self, mock_api_client):
        """测试查询None任务ID"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_task_status(task_id=None)
        assert "任务ID不能为空" in str(exc_info.value)
    
    def test_query_batch_status_success(self, mock_api_client, mock_response_success):
        """测试查询批次状态成功"""
        mock_api_client.get.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.query_batch_status(batch_no="BATCH001")
        
        assert result["code"] == "0000"
        mock_api_client.get.assert_called_once()
        
        # 验证请求参数
        call_args = mock_api_client.get.call_args
        assert "task/batch" in call_args[0][0]
        assert call_args[1]["params"]["batchNo"] == "BATCH001"
    
    def test_query_batch_status_empty_batch_no(self, mock_api_client):
        """测试查询空批次号"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_batch_status(batch_no="")
        assert "批次号不能为空" in str(exc_info.value)
    
    def test_query_upload_progress_success(self, mock_api_client, mock_response_success):
        """测试查询上传进度成功"""
        mock_api_client.get.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.query_upload_progress(upload_id="UPLOAD123")
        
        assert result["code"] == "0000"
        mock_api_client.get.assert_called_once()
        
        # 验证请求参数
        call_args = mock_api_client.get.call_args
        assert "task/upload" in call_args[0][0]
        assert call_args[1]["params"]["uploadId"] == "UPLOAD123"
    
    def test_query_upload_progress_empty_id(self, mock_api_client):
        """测试查询空上传ID"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_upload_progress(upload_id="")
        assert "上传ID不能为空" in str(exc_info.value)
    
    def test_get_task_list_success(self, mock_api_client, mock_response_success):
        """测试获取任务列表成功"""
        mock_api_client.get.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.get_task_list(
            start_date="2023-01-01",
            end_date="2023-01-31",
            page=1,
            page_size=20
        )
        
        assert result["code"] == "0000"
        mock_api_client.get.assert_called_once()
        
        # 验证请求参数
        call_args = mock_api_client.get.call_args
        params = call_args[1]["params"]
        assert params["startDate"] == "2023-01-01"
        assert params["endDate"] == "2023-01-31"
        assert params["page"] == 1
        assert params["pageSize"] == 20
    
    def test_get_task_list_invalid_date_format(self, mock_api_client):
        """测试无效日期格式"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.get_task_list(
                start_date="invalid-date",
                end_date="2023-01-31"
            )
        assert "日期格式不正确" in str(exc_info.value)
    
    def test_get_task_list_invalid_page(self, mock_api_client):
        """测试无效页码"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.get_task_list(
                start_date="2023-01-01",
                end_date="2023-01-31",
                page=0
            )
        assert "页码必须大于0" in str(exc_info.value)
    
    def test_get_task_list_invalid_page_size(self, mock_api_client):
        """测试无效页面大小"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.get_task_list(
                start_date="2023-01-01",
                end_date="2023-01-31",
                page_size=0
            )
        assert "每页大小必须在1-100之间" in str(exc_info.value)
    
    def test_get_task_list_page_size_too_large(self, mock_api_client):
        """测试页面大小过大"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.get_task_list(
                start_date="2023-01-01",
                end_date="2023-01-31",
                page_size=101
            )
        assert "每页大小必须在1-100之间" in str(exc_info.value)
    
    def test_get_task_list_with_status_filter(self, mock_api_client, mock_response_success):
        """测试带状态过滤的任务列表查询"""
        mock_api_client.get.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.get_task_list(
            start_date="2023-01-01",
            end_date="2023-01-31",
            status="completed"
        )
        
        assert result["code"] == "0000"
        
        # 验证状态参数
        call_args = mock_api_client.get.call_args
        params = call_args[1]["params"]
        assert params["status"] == "completed"
    
    def test_get_task_list_with_task_type_filter(self, mock_api_client, mock_response_success):
        """测试带任务类型过滤的任务列表查询"""
        mock_api_client.get.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.get_task_list(
            start_date="2023-01-01",
            end_date="2023-01-31",
            task_type="upload"
        )
        
        assert result["code"] == "0000"
        
        # 验证任务类型参数
        call_args = mock_api_client.get.call_args
        params = call_args[1]["params"]
        assert params["taskType"] == "upload"
    
    def test_cancel_task_success(self, mock_api_client, mock_response_success):
        """测试取消任务成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.cancel_task(task_id="TASK123456789")
        
        assert result["code"] == "0000"
        mock_api_client.post.assert_called_once()
        
        # 验证请求参数
        call_args = mock_api_client.post.call_args
        assert "task/cancel" in call_args[0][0]
        assert call_args[1]["data"]["taskId"] == "TASK123456789"
    
    def test_cancel_task_empty_id(self, mock_api_client):
        """测试取消空任务ID"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.cancel_task(task_id="")
        assert "任务ID不能为空" in str(exc_info.value)
    
    def test_retry_task_success(self, mock_api_client, mock_response_success):
        """测试重试任务成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = TaskAPI(client=mock_api_client)
        result = api.retry_task(task_id="TASK123456789")
        
        assert result["code"] == "0000"
        mock_api_client.post.assert_called_once()
        
        # 验证请求参数
        call_args = mock_api_client.post.call_args
        assert "task/retry" in call_args[0][0]
        assert call_args[1]["data"]["taskId"] == "TASK123456789"
    
    def test_retry_task_empty_id(self, mock_api_client):
        """测试重试空任务ID"""
        api = TaskAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.retry_task(task_id="")
        assert "任务ID不能为空" in str(exc_info.value)
    
    def test_context_manager(self):
        """测试上下文管理器"""
        with TaskAPI() as api:
            assert isinstance(api, TaskAPI)
            assert api.client is not None
    
    def test_close_own_client(self):
        """测试关闭自有客户端"""
        api = TaskAPI()
        client = api.client
        
        api.close()
        
        # 验证客户端被关闭
        assert api.client is None
    
    def test_close_external_client(self, mock_api_client):
        """测试不关闭外部客户端"""
        api = TaskAPI(client=mock_api_client)
        
        api.close()
        
        # 外部客户端不应该被关闭
        assert api.client == mock_api_client
