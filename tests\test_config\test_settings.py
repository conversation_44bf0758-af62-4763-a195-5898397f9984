"""
配置模块测试
"""

import pytest
from unittest.mock import patch

from nifa.config.settings import Settings, Environment
from nifa.exceptions.base import NifaConfigurationError


class TestSettings:
    """设置类测试"""
    
    def test_default_settings(self):
        """测试默认设置"""
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            ENVIRONMENT="development",
            NIFA_BASE_URL="https://api.nifa.org.cn"
        )

        assert settings.ENVIRONMENT == Environment.DEVELOPMENT
        assert settings.DEBUG is True
        assert settings.NIFA_BASE_URL == "https://api.nifa.org.cn"
        assert settings.NIFA_TIMEOUT == 30
        assert settings.NIFA_MAX_RETRIES == 3
        assert settings.LOG_LEVEL == "INFO"
    
    def test_environment_validation(self):
        """测试环境验证"""
        # 测试有效环境
        settings = Settings(NIFA_ORG_CODE="TEST123", ENVIRONMENT="production")
        assert settings.ENVIRONMENT == Environment.PRODUCTION
        
        # 测试无效环境回退到默认值
        settings = Settings(NIFA_ORG_CODE="TEST123", ENVIRONMENT="invalid")
        assert settings.ENVIRONMENT == Environment.DEVELOPMENT
    
    def test_debug_mode_in_production(self):
        """测试生产环境下调试模式自动关闭"""
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            ENVIRONMENT="production",
            DEBUG=True
        )
        assert settings.DEBUG is False
    
    def test_base_url_validation(self):
        """测试基础URL验证"""
        # 测试有效URL
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            NIFA_BASE_URL="https://test.example.com"
        )
        assert settings.NIFA_BASE_URL == "https://test.example.com"
        
        # 测试URL末尾斜杠自动移除
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            NIFA_BASE_URL="https://test.example.com/"
        )
        assert settings.NIFA_BASE_URL == "https://test.example.com"
        
        # 测试无效URL
        with pytest.raises(ValueError):
            Settings(
                NIFA_ORG_CODE="TEST123",
                NIFA_BASE_URL="invalid-url"
            )
    
    def test_timeout_validation(self):
        """测试超时时间验证"""
        # 测试有效超时时间
        settings = Settings(NIFA_ORG_CODE="TEST123", NIFA_TIMEOUT=60)
        assert settings.NIFA_TIMEOUT == 60
        
        # 测试无效超时时间（负数）
        settings = Settings(NIFA_ORG_CODE="TEST123", NIFA_TIMEOUT=-10)
        assert settings.NIFA_TIMEOUT == 30  # 回退到默认值
        
        # 测试超大超时时间
        settings = Settings(NIFA_ORG_CODE="TEST123", NIFA_TIMEOUT=500)
        assert settings.NIFA_TIMEOUT == 300  # 限制为最大值
    
    def test_retry_validation(self):
        """测试重试次数验证"""
        # 测试有效重试次数
        settings = Settings(NIFA_ORG_CODE="TEST123", NIFA_MAX_RETRIES=5)
        assert settings.NIFA_MAX_RETRIES == 5
        
        # 测试负数重试次数
        settings = Settings(NIFA_ORG_CODE="TEST123", NIFA_MAX_RETRIES=-1)
        assert settings.NIFA_MAX_RETRIES == 0
        
        # 测试超大重试次数
        settings = Settings(NIFA_ORG_CODE="TEST123", NIFA_MAX_RETRIES=20)
        assert settings.NIFA_MAX_RETRIES == 10  # 限制为最大值
    
    def test_log_level_validation(self):
        """测试日志级别验证"""
        # 测试有效日志级别
        settings = Settings(NIFA_ORG_CODE="TEST123", LOG_LEVEL="DEBUG")
        assert settings.LOG_LEVEL == "DEBUG"
        
        # 测试无效日志级别
        settings = Settings(NIFA_ORG_CODE="TEST123", LOG_LEVEL="INVALID")
        assert settings.LOG_LEVEL == "INFO"  # 回退到默认值
        
        # 测试小写日志级别
        settings = Settings(NIFA_ORG_CODE="TEST123", LOG_LEVEL="debug")
        assert settings.LOG_LEVEL == "DEBUG"  # 自动转换为大写
    
    def test_connection_pool_validation(self):
        """测试连接池配置验证"""
        # 测试有效连接池大小
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            CONNECTION_POOL_SIZE=5,
            CONNECTION_POOL_MAX_SIZE=15
        )
        assert settings.CONNECTION_POOL_SIZE == 5
        assert settings.CONNECTION_POOL_MAX_SIZE == 15
        
        # 测试最大连接池小于基础连接池
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            CONNECTION_POOL_SIZE=20,
            CONNECTION_POOL_MAX_SIZE=10
        )
        assert settings.CONNECTION_POOL_MAX_SIZE >= settings.CONNECTION_POOL_SIZE
    
    def test_get_api_url(self):
        """测试API URL构建"""
        settings = Settings(
            NIFA_ORG_CODE="TEST123",
            NIFA_BASE_URL="https://api.example.com"
        )
        
        # 测试普通端点
        url = settings.get_api_url("test/endpoint")
        assert url == "https://api.example.com/test/endpoint"
        
        # 测试带前导斜杠的端点
        url = settings.get_api_url("/test/endpoint")
        assert url == "https://api.example.com/test/endpoint"
    
    def test_environment_helpers(self):
        """测试环境辅助方法"""
        # 测试开发环境
        dev_settings = Settings(
            NIFA_ORG_CODE="TEST123",
            ENVIRONMENT="development"
        )
        assert dev_settings.is_development() is True
        assert dev_settings.is_production() is False
        
        # 测试生产环境
        prod_settings = Settings(
            NIFA_ORG_CODE="TEST123",
            ENVIRONMENT="production"
        )
        assert prod_settings.is_development() is False
        assert prod_settings.is_production() is True
    
    def test_log_config(self):
        """测试日志配置"""
        # 测试开发环境日志配置
        dev_settings = Settings(
            NIFA_ORG_CODE="TEST123",
            ENVIRONMENT="development"
        )
        log_config = dev_settings.get_log_config()
        assert "console" in log_config["handlers"]
        
        # 测试生产环境日志配置
        prod_settings = Settings(
            NIFA_ORG_CODE="TEST123",
            ENVIRONMENT="production"
        )
        log_config = prod_settings.get_log_config()
        assert "file" in log_config["handlers"]
    
    @patch.dict("os.environ", {"NIFA_ORG_CODE": "ENV_TEST123"})
    def test_environment_variable_loading(self):
        """测试环境变量加载"""
        settings = Settings()
        assert settings.NIFA_ORG_CODE == "ENV_TEST123"


class TestEnvironment:
    """环境枚举测试"""
    
    def test_environment_values(self):
        """测试环境枚举值"""
        assert Environment.DEVELOPMENT == "development"
        assert Environment.TESTING == "testing"
        assert Environment.STAGING == "staging"
        assert Environment.PRODUCTION == "production"
    
    def test_environment_comparison(self):
        """测试环境比较"""
        assert Environment.DEVELOPMENT != Environment.PRODUCTION
        assert Environment.TESTING == "testing"
