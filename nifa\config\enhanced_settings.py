"""
增强的配置管理模块
支持配置验证、环境切换、热重载等高级特性
"""

import os
from enum import Enum
from typing import Optional, Dict, Any, List, Union
from pathlib import Path
from functools import lru_cache

from pydantic import Field, validator, root_validator
from pydantic_settings import BaseSettings


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class SignatureAlgorithm(str, Enum):
    """签名算法枚举"""
    SHA256 = "SHA256"
    SHA512 = "SHA512"  # 移除不安全的MD5


class Environment(str, Enum):
    """环境枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class CacheConfig(BaseSettings):
    """缓存配置"""
    enabled: bool = Field(default=False, description="是否启用缓存")
    ttl: int = Field(default=300, ge=0, description="缓存过期时间（秒）")
    max_size: int = Field(default=1000, ge=1, description="缓存最大条目数")
    backend: str = Field(default="memory", description="缓存后端类型")
    
    @validator('backend')
    def validate_backend(cls, v):
        allowed_backends = ['memory', 'redis', 'memcached']
        if v not in allowed_backends:
            raise ValueError(f'缓存后端必须是: {", ".join(allowed_backends)}')
        return v


class RetryConfig(BaseSettings):
    """重试配置"""
    max_attempts: int = Field(default=3, ge=1, le=10, description="最大重试次数")
    base_delay: float = Field(default=1.0, ge=0.1, description="基础延迟时间（秒）")
    max_delay: float = Field(default=60.0, ge=1.0, description="最大延迟时间（秒）")
    backoff_factor: float = Field(default=2.0, ge=1.0, description="退避因子")
    jitter: bool = Field(default=True, description="是否添加随机抖动")


class SecurityConfig(BaseSettings):
    """安全配置"""
    signature_algorithm: SignatureAlgorithm = Field(
        default=SignatureAlgorithm.SHA256,
        description="签名算法"
    )
    request_timeout: int = Field(default=30, ge=1, le=300, description="请求超时时间（秒）")
    max_request_size: int = Field(default=10*1024*1024, description="最大请求大小（字节）")
    allowed_hosts: List[str] = Field(default_factory=list, description="允许的主机列表")
    
    # 加密相关
    sm2_public_key: Optional[str] = Field(default=None, description="SM2公钥")
    sm2_private_key: Optional[str] = Field(default=None, description="SM2私钥")
    sm4_key: Optional[str] = Field(default=None, description="SM4密钥")
    
    @validator('sm2_public_key', 'sm2_private_key')
    def validate_sm2_key(cls, v):
        if v and not v.startswith(('04', '02', '03')):
            raise ValueError('SM2密钥格式不正确')
        return v
    
    @validator('sm4_key')
    def validate_sm4_key(cls, v):
        if v and len(v) != 32:
            raise ValueError('SM4密钥必须是32位十六进制字符串')
        return v


class MonitoringConfig(BaseSettings):
    """监控配置"""
    metrics_enabled: bool = Field(default=False, description="是否启用指标收集")
    tracing_enabled: bool = Field(default=False, description="是否启用链路追踪")
    health_check_enabled: bool = Field(default=True, description="是否启用健康检查")
    
    # Prometheus配置
    prometheus_port: int = Field(default=8080, ge=1024, le=65535)
    
    # Jaeger配置
    jaeger_endpoint: Optional[str] = Field(default=None)
    service_name: str = Field(default="nifa-client")


class EnhancedSettings(BaseSettings):
    """增强的应用配置类"""
    
    # 基础配置
    environment: Environment = Field(default=Environment.DEVELOPMENT)
    debug: bool = Field(default=True)
    log_level: LogLevel = Field(default=LogLevel.INFO)
    
    # NIFA API配置
    nifa_base_url: str = Field(
        default="https://api.nifa.org.cn",
        description="NIFA API基础URL"
    )
    nifa_org_code: str = Field(description="机构代码")
    
    # 子配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    retry: RetryConfig = Field(default_factory=RetryConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    
    # 连接池配置
    connection_pool_size: int = Field(default=10, ge=1, le=100)
    connection_pool_max_size: int = Field(default=20, ge=1, le=200)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_nested_delimiter = "__"  # 支持嵌套配置 CACHE__TTL=300
        
        @classmethod
        def customise_sources(cls, init_settings, env_settings, file_secret_settings):
            return (init_settings, env_settings, file_secret_settings)
    
    @root_validator
    def validate_environment_specific_settings(cls, values):
        """环境特定的配置验证"""
        env = values.get('environment')
        
        if env == Environment.PRODUCTION:
            # 生产环境强制要求
            if values.get('debug', True):
                values['debug'] = False
            if values.get('log_level') == LogLevel.DEBUG:
                values['log_level'] = LogLevel.INFO
        
        return values
    
    @validator('nifa_org_code')
    def validate_org_code(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('机构代码不能为空')
        if len(v) < 6:
            raise ValueError('机构代码长度不能少于6位')
        return v.strip()
    
    def get_api_url(self, endpoint: str) -> str:
        """获取完整的API URL"""
        return f"{self.nifa_base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == Environment.PRODUCTION
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "level": self.log_level.value,
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "handlers": ["console"] if self.is_development() else ["file", "console"],
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.dict()
    
    def reload_from_env(self) -> None:
        """从环境变量重新加载配置"""
        # 实现配置热重载逻辑
        pass


@lru_cache()
def get_settings() -> EnhancedSettings:
    """获取全局配置实例（单例模式）"""
    return EnhancedSettings()


# 向后兼容
settings = get_settings()