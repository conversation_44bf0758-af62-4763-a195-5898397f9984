<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">55%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-03 16:50 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_9589fface2250470___init___py.html">nifa\__init__.py</a></td>
                <td class="name left"><a href="z_9589fface2250470___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838___init___py.html">nifa\api\__init__.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t37">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t37"><data value='init__'>APIClient.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t70">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t70"><data value='create_session'>APIClient._create_session</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t106">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t106"><data value='build_url'>APIClient._build_url</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t118">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t118"><data value='validate_response'>APIClient._validate_response</data></a></td>
                <td>17</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="16 17">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t172">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t172"><data value='request_context'>APIClient._request_context</data></a></td>
                <td>19</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="16 19">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t221">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t221"><data value='request'>APIClient.request</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t281">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t281"><data value='get'>APIClient.get</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t285">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t285"><data value='post'>APIClient.post</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t289">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t289"><data value='put'>APIClient.put</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t293">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t293"><data value='delete'>APIClient.delete</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t297">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t297"><data value='close'>APIClient.close</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t302">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t302"><data value='enter__'>APIClient.__enter__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t306">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html#t306"><data value='exit__'>APIClient.__exit__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html">nifa\api\client.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t22">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t22"><data value='init__'>DataAPI.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t32">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t32"><data value='upload_data'>DataAPI.upload_data</data></a></td>
                <td>25</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="15 25">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t104">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t104"><data value='upload_file'>DataAPI.upload_file</data></a></td>
                <td>30</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="17 30">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t190">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t190"><data value='batch_upload_data'>DataAPI.batch_upload_data</data></a></td>
                <td>35</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="22 35">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t281">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t281"><data value='get_upload_status'>DataAPI.get_upload_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t316">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t316"><data value='close'>DataAPI.close</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t321">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t321"><data value='enter__'>DataAPI.__enter__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t325">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html#t325"><data value='exit__'>DataAPI.__exit__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html">nifa\api\data.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t19">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t19"><data value='init__'>InfoAPI.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t29">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t29"><data value='query_personal_credit'>InfoAPI.query_personal_credit</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t83">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t83"><data value='query_enterprise_credit'>InfoAPI.query_enterprise_credit</data></a></td>
                <td>11</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="9 11">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t140">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t140"><data value='query_credit_report'>InfoAPI.query_credit_report</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t188">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t188"><data value='batch_query_personal_credit'>InfoAPI.batch_query_personal_credit</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="15 16">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t256">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t256"><data value='get_query_history'>InfoAPI.get_query_history</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t316">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t316"><data value='close'>InfoAPI.close</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t321">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t321"><data value='enter__'>InfoAPI.__enter__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t325">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html#t325"><data value='exit__'>InfoAPI.__exit__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html">nifa\api\info.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_info_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t19">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t19"><data value='init__'>JudicialAPI.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t29">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t29"><data value='query_court_records'>JudicialAPI.query_court_records</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t80">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t80"><data value='query_enforcement_records'>JudicialAPI.query_enforcement_records</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t121">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t121"><data value='query_dishonest_records'>JudicialAPI.query_dishonest_records</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t162">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t162"><data value='query_restricted_records'>JudicialAPI.query_restricted_records</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t203">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t203"><data value='query_enterprise_judicial_records'>JudicialAPI.query_enterprise_judicial_records</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t257">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t257"><data value='batch_query_judicial_records'>JudicialAPI.batch_query_judicial_records</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t322">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t322"><data value='get_judicial_summary'>JudicialAPI.get_judicial_summary</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t363">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t363"><data value='close'>JudicialAPI.close</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t368">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t368"><data value='enter__'>JudicialAPI.__enter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t372">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html#t372"><data value='exit__'>JudicialAPI.__exit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html">nifa\api\judicial.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_judicial_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t19">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t19"><data value='init__'>QueryCountAPI.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t29">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t29"><data value='get_query_count'>QueryCountAPI.get_query_count</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t75">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t75"><data value='get_remaining_quota'>QueryCountAPI.get_remaining_quota</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t114">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t114"><data value='get_usage_detail'>QueryCountAPI.get_usage_detail</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t178">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t178"><data value='get_cost_summary'>QueryCountAPI.get_cost_summary</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t226">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t226"><data value='get_service_statistics'>QueryCountAPI.get_service_statistics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t269">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t269"><data value='export_usage_report'>QueryCountAPI.export_usage_report</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t317">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t317"><data value='close'>QueryCountAPI.close</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t322">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t322"><data value='enter__'>QueryCountAPI.__enter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t326">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html#t326"><data value='exit__'>QueryCountAPI.__exit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html">nifa\api\query_count.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_query_count_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t19">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t19"><data value='init__'>TaskAPI.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t29">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t29"><data value='query_task_status'>TaskAPI.query_task_status</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t64">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t64"><data value='query_batch_task_status'>TaskAPI.query_batch_task_status</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t115">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t115"><data value='get_task_list'>TaskAPI.get_task_list</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t179">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t179"><data value='get_task_detail'>TaskAPI.get_task_detail</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t217">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t217"><data value='retry_failed_task'>TaskAPI.retry_failed_task</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t252">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t252"><data value='cancel_task'>TaskAPI.cancel_task</data></a></td>
                <td>8</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="7 8">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t292">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t292"><data value='get_task_statistics'>TaskAPI.get_task_statistics</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t340">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t340"><data value='close'>TaskAPI.close</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t345">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t345"><data value='enter__'>TaskAPI.__enter__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t349">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html#t349"><data value='exit__'>TaskAPI.__exit__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html">nifa\api\task.py</a></td>
                <td class="name left"><a href="z_aee3eced65d61838_task_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2___init___py.html">nifa\auth\__init__.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t30">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t30"><data value='init__'>SM2Encryption.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t54">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t54"><data value='encrypt'>SM2Encryption.encrypt</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t84">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t84"><data value='decrypt'>SM2Encryption.decrypt</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t113">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t113"><data value='sign'>SM2Encryption.sign</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t143">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t143"><data value='verify'>SM2Encryption.verify</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t178">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t178"><data value='init__'>SM4Encryption.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t203">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t203"><data value='encrypt'>SM4Encryption.encrypt</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t233">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t233"><data value='decrypt'>SM4Encryption.decrypt</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t266">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t266"><data value='create_sm2_encryption'>create_sm2_encryption</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t280">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html#t280"><data value='create_sm4_encryption'>create_sm4_encryption</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html">nifa\auth\encryption.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_encryption_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="22 28">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t22">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t22"><data value='generate_random_code'>generate_random_code</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t35">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t35"><data value='sha256_sign'>sha256_sign</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t58">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t58"><data value='md5_sign'>md5_sign</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t74">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t74"><data value='init__'>SignatureBuilder.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t85">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t85"><data value='build_signature_string'>SignatureBuilder.build_signature_string</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t125">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t125"><data value='build_signature'>SignatureBuilder.build_signature</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t149">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t149"><data value='sign_request'>SignatureBuilder.sign_request</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t166">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t166"><data value='verify_signature'>SignatureBuilder.verify_signature</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t191">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t191"><data value='with_signature'>with_signature</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t211">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t211"><data value='decorator'>with_signature.decorator</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t213">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t213"><data value='wrapper'>with_signature.decorator.wrapper</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t257">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t257"><data value='init__'>RequestSigner.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t275">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t275"><data value='sign_request_data'>RequestSigner.sign_request_data</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t299">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html#t299"><data value='verify_response_signature'>RequestSigner.verify_response_signature</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html">nifa\auth\signature.py</a></td>
                <td class="name left"><a href="z_e30577f822d2f7f2_signature_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595___init___py.html">nifa\config\__init__.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t47">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t47"><data value='validate_backend'>CacheConfig.validate_backend</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t79">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t79"><data value='validate_sm2_key'>SecurityConfig.validate_sm2_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t85">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t85"><data value='validate_sm4_key'>SecurityConfig.validate_sm4_key</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t137">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t137"><data value='customise_sources'>EnhancedSettings.Config.customise_sources</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t141">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t141"><data value='validate_environment_specific_settings'>EnhancedSettings.validate_environment_specific_settings</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t155">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t155"><data value='validate_org_code'>EnhancedSettings.validate_org_code</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t162">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t162"><data value='get_api_url'>EnhancedSettings.get_api_url</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t166">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t166"><data value='is_development'>EnhancedSettings.is_development</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t170">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t170"><data value='is_production'>EnhancedSettings.is_production</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t174">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t174"><data value='get_log_config'>EnhancedSettings.get_log_config</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t182">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t182"><data value='to_dict'>EnhancedSettings.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t186">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t186"><data value='reload_from_env'>EnhancedSettings.reload_from_env</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t193">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html#t193"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html">nifa\config\enhanced_settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_enhanced_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t123">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t123"><data value='validate_environment'>Settings.validate_environment</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t134">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t134"><data value='validate_debug_mode'>Settings.validate_debug_mode</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t142">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t142"><data value='validate_base_url'>Settings.validate_base_url</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t154">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t154"><data value='validate_timeout'>Settings.validate_timeout</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t164">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t164"><data value='validate_max_retries'>Settings.validate_max_retries</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t174">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t174"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t185">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t185"><data value='validate_connection_pool_max_size'>Settings.validate_connection_pool_max_size</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t205">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t205"><data value='customise_sources'>Settings.Config.customise_sources</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t218">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t218"><data value='get_api_url'>Settings.get_api_url</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t222">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t222"><data value='is_development'>Settings.is_development</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t226">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t226"><data value='is_production'>Settings.is_production</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t230">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html#t230"><data value='get_log_config'>Settings.get_log_config</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html">nifa\config\settings.py</a></td>
                <td class="name left"><a href="z_9f8c89c1e0a6a595_settings_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>63</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="63 63">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5___init___py.html">nifa\core\__init__.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t41">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t41"><data value='init__'>CircuitBreaker.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t49">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t49"><data value='call__'>CircuitBreaker.__call__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t51">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t51"><data value='wrapper'>CircuitBreaker.__call__.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t55">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t55"><data value='call'>CircuitBreaker.call</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t86">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t86"><data value='should_attempt_reset'>CircuitBreaker._should_attempt_reset</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t90">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t90"><data value='on_success'>CircuitBreaker._on_success</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t101">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t101"><data value='on_failure'>CircuitBreaker._on_failure</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t114">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t114"><data value='get_state'>CircuitBreaker.get_state</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t118">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t118"><data value='get_stats'>CircuitBreaker.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t127">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t127"><data value='reset'>CircuitBreaker.reset</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t140">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t140"><data value='init__'>CircuitBreakerManager.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t144">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t144"><data value='get_breaker'>CircuitBreakerManager.get_breaker</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t153">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t153"><data value='remove_breaker'>CircuitBreakerManager.remove_breaker</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t158">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t158"><data value='get_all_stats'>CircuitBreakerManager.get_all_stats</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t163">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html#t163"><data value='reset_all'>CircuitBreakerManager.reset_all</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html">nifa\core\circuit_breaker.py</a></td>
                <td class="name left"><a href="z_4bd54dd4baa390e5_circuit_breaker_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440___init___py.html">nifa\exceptions\__init__.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t13">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t13"><data value='init__'>NifaError.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t29">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t29"><data value='str__'>NifaError.__str__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t34">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t34"><data value='repr__'>NifaError.__repr__</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t41">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t41"><data value='init__'>NifaAPIError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t56">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t56"><data value='init__'>NifaResponseError.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t88">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t88"><data value='init__'>NifaValidationError.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t104">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t104"><data value='init__'>NifaTimeoutError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t118">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t118"><data value='init__'>NifaNetworkError.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t141">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t141"><data value='init__'>NifaRateLimitError.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t177">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html#t177"><data value='create_exception_from_response'>create_exception_from_response</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html">nifa\exceptions\base.py</a></td>
                <td class="name left"><a href="z_2cbf57cf94805440_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76___init___py.html">nifa\models\__init__.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t45">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t45"><data value='validate_code'>BaseResponse.validate_code</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t50">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t50"><data value='is_success'>BaseResponse.is_success</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t54">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t54"><data value='get_error_message'>BaseResponse.get_error_message</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t77">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t77"><data value='calculate_pages'>PaginatedData.calculate_pages</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t84">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t84"><data value='calculate_has_next'>PaginatedData.calculate_has_next</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t91">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t91"><data value='calculate_has_prev'>PaginatedData.calculate_has_prev</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t123">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html#t123"><data value='is_healthy'>HealthCheckResponse.is_healthy</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html">nifa\models\base.py</a></td>
                <td class="name left"><a href="z_b652cb6b2a49ef76_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe___init___py.html">nifa\utils\__init__.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t21">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t21"><data value='format_datetime'>format_datetime</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t37">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t37"><data value='parse_response'>parse_response</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t57">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t57"><data value='generate_request_id'>generate_request_id</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t69">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t69"><data value='calculate_md5'>calculate_md5</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t84">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t84"><data value='calculate_sha256'>calculate_sha256</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t99">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t99"><data value='retry_on_failure'>retry_on_failure</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t117">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t117"><data value='decorator'>retry_on_failure.decorator</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t119">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t119"><data value='wrapper'>retry_on_failure.decorator.wrapper</data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t157">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t157"><data value='safe_get'>safe_get</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t184">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t184"><data value='mask_sensitive_data'>mask_sensitive_data</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t207">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t207"><data value='validate_response_structure'>validate_response_structure</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t233">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t233"><data value='log_request_response'>log_request_response</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t255">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t255"><data value='mask_dict'>log_request_response.mask_dict</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t281">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t281"><data value='convert_to_snake_case'>convert_to_snake_case</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t296">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html#t296"><data value='convert_to_camel_case'>convert_to_camel_case</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html">nifa\utils\helpers.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_helpers_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t13">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t13"><data value='validate_id_card'>validate_id_card</data></a></td>
                <td>20</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="19 20">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t79">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t79"><data value='validate_name'>validate_name</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t116">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t116"><data value='validate_phone'>validate_phone</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t145">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t145"><data value='validate_org_code'>validate_org_code</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t174">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t174"><data value='validate_date_string'>validate_date_string</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t202">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t202"><data value='validate_amount'>validate_amount</data></a></td>
                <td>13</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="11 13">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t251">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html#t251"><data value='validate_required_fields'>validate_required_fields</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html">nifa\utils\validators.py</a></td>
                <td class="name left"><a href="z_1b14f832205fdefe_validators_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1436</td>
                <td>652</td>
                <td>0</td>
                <td class="right" data-ratio="784 1436">55%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-03 16:50 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
