#!/usr/bin/env python3
"""
依赖关系分析工具
分析NIFA项目中各个模块之间的依赖关系
"""

import ast
import os
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict, deque
import json


class DependencyAnalyzer:
    """依赖关系分析器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.nifa_root = self.project_root / "nifa"
        self.dependencies = defaultdict(set)  # module -> set of dependencies
        self.reverse_dependencies = defaultdict(set)  # module -> set of dependents
        self.external_dependencies = defaultdict(set)  # module -> set of external deps
        self.all_modules = set()
        
    def analyze(self) -> Dict:
        """执行完整的依赖关系分析"""
        print("🔍 开始分析项目依赖关系...")
        
        # 扫描所有Python文件
        self._scan_python_files()
        
        # 分析循环依赖
        cycles = self._find_cycles()
        
        # 分析依赖层次
        layers = self._analyze_layers()
        
        # 分析耦合度
        coupling_analysis = self._analyze_coupling()
        
        # 分析外部依赖
        external_deps = self._analyze_external_dependencies()
        
        # 生成报告
        report = {
            "summary": {
                "total_modules": len(self.all_modules),
                "total_internal_dependencies": sum(len(deps) for deps in self.dependencies.values()),
                "total_external_dependencies": len(external_deps),
                "circular_dependencies": len(cycles),
                "dependency_layers": len(layers)
            },
            "modules": list(self.all_modules),
            "internal_dependencies": {k: list(v) for k, v in self.dependencies.items()},
            "external_dependencies": external_deps,
            "circular_dependencies": cycles,
            "dependency_layers": layers,
            "coupling_analysis": coupling_analysis
        }
        
        return report
    
    def _scan_python_files(self):
        """扫描所有Python文件并分析导入语句"""
        for py_file in self.nifa_root.rglob("*.py"):
            if py_file.name == "__pycache__":
                continue
                
            module_name = self._get_module_name(py_file)
            self.all_modules.add(module_name)
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = self._extract_imports(tree)
                
                for imp in imports:
                    if imp.startswith('nifa.') or imp == 'nifa':
                        # 内部依赖
                        self.dependencies[module_name].add(imp)
                        self.reverse_dependencies[imp].add(module_name)
                    else:
                        # 外部依赖
                        self.external_dependencies[module_name].add(imp)
                        
            except Exception as e:
                print(f"⚠️  分析文件 {py_file} 时出错: {e}")
    
    def _get_module_name(self, file_path: Path) -> str:
        """获取模块名称"""
        relative_path = file_path.relative_to(self.project_root)
        parts = list(relative_path.parts)
        
        # 移除.py扩展名
        if parts[-1].endswith('.py'):
            parts[-1] = parts[-1][:-3]
        
        # 如果是__init__.py，移除最后一部分
        if parts[-1] == '__init__':
            parts = parts[:-1]
        
        return '.'.join(parts)
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """提取导入语句"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    if node.level > 0:  # 相对导入
                        # 处理相对导入
                        base_module = 'nifa'  # 简化处理
                        if node.module:
                            imports.append(f"{base_module}.{node.module}")
                        else:
                            imports.append(base_module)
                    else:
                        imports.append(node.module)
        
        return imports
    
    def _find_cycles(self) -> List[List[str]]:
        """查找循环依赖"""
        cycles = []
        visited = set()
        rec_stack = set()
        
        def dfs(node: str, path: List[str]) -> bool:
            if node in rec_stack:
                # 找到循环
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in self.dependencies.get(node, []):
                if neighbor in self.all_modules:  # 只考虑内部模块
                    dfs(neighbor, path)
            
            rec_stack.remove(node)
            path.pop()
            return False
        
        for module in self.all_modules:
            if module not in visited:
                dfs(module, [])
        
        return cycles
    
    def _analyze_layers(self) -> List[List[str]]:
        """分析依赖层次"""
        # 使用拓扑排序分析层次
        in_degree = defaultdict(int)
        
        # 计算入度
        for module in self.all_modules:
            for dep in self.dependencies.get(module, []):
                if dep in self.all_modules:
                    in_degree[dep] += 1
        
        # 初始化队列（入度为0的节点）
        queue = deque([module for module in self.all_modules if in_degree[module] == 0])
        layers = []
        
        while queue:
            current_layer = []
            next_queue = deque()
            
            while queue:
                module = queue.popleft()
                current_layer.append(module)
                
                # 更新邻居的入度
                for neighbor in self.reverse_dependencies.get(module, []):
                    if neighbor in self.all_modules:
                        in_degree[neighbor] -= 1
                        if in_degree[neighbor] == 0:
                            next_queue.append(neighbor)
            
            if current_layer:
                layers.append(current_layer)
            queue = next_queue
        
        return layers
    
    def _analyze_coupling(self) -> Dict:
        """分析模块耦合度"""
        coupling_data = {}
        
        for module in self.all_modules:
            deps = self.dependencies.get(module, set())
            dependents = self.reverse_dependencies.get(module, set())
            
            # 只考虑内部依赖
            internal_deps = [d for d in deps if d in self.all_modules]
            internal_dependents = [d for d in dependents if d in self.all_modules]
            
            coupling_data[module] = {
                "afferent_coupling": len(internal_dependents),  # 传入耦合
                "efferent_coupling": len(internal_deps),        # 传出耦合
                "instability": len(internal_deps) / (len(internal_deps) + len(internal_dependents)) if (len(internal_deps) + len(internal_dependents)) > 0 else 0,
                "dependencies": internal_deps,
                "dependents": internal_dependents
            }
        
        return coupling_data
    
    def _analyze_external_dependencies(self) -> Dict[str, int]:
        """分析外部依赖使用情况"""
        external_deps = defaultdict(int)
        
        for module_deps in self.external_dependencies.values():
            for dep in module_deps:
                # 提取顶级包名
                top_level = dep.split('.')[0]
                external_deps[top_level] += 1
        
        return dict(external_deps)
    
    def generate_mermaid_diagram(self, report: Dict) -> str:
        """生成Mermaid依赖关系图"""
        lines = ["graph TD"]
        
        # 添加节点
        for module in report["modules"]:
            clean_name = module.replace(".", "_").replace("-", "_")
            display_name = module.split(".")[-1] if "." in module else module
            lines.append(f'    {clean_name}["{display_name}"]')
        
        # 添加依赖关系
        for module, deps in report["internal_dependencies"].items():
            clean_module = module.replace(".", "_").replace("-", "_")
            for dep in deps:
                if dep in report["modules"]:
                    clean_dep = dep.replace(".", "_").replace("-", "_")
                    lines.append(f'    {clean_module} --> {clean_dep}')
        
        # 标记循环依赖
        for cycle in report["circular_dependencies"]:
            for i in range(len(cycle) - 1):
                clean_from = cycle[i].replace(".", "_").replace("-", "_")
                clean_to = cycle[i + 1].replace(".", "_").replace("-", "_")
                lines.append(f'    {clean_from} -.-> {clean_to}')
                lines.append(f'    style {clean_from} fill:#ffcccc')
                lines.append(f'    style {clean_to} fill:#ffcccc')
        
        return "\n".join(lines)


def main():
    """主函数"""
    project_root = os.getcwd()
    analyzer = DependencyAnalyzer(project_root)
    
    # 执行分析
    report = analyzer.analyze()
    
    # 保存分析报告
    with open("dependency_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成Mermaid图
    mermaid_diagram = analyzer.generate_mermaid_diagram(report)
    with open("dependency_diagram.mmd", "w", encoding="utf-8") as f:
        f.write(mermaid_diagram)
    
    # 打印摘要
    print("\n📊 依赖关系分析报告")
    print("=" * 50)
    print(f"总模块数: {report['summary']['total_modules']}")
    print(f"内部依赖数: {report['summary']['total_internal_dependencies']}")
    print(f"外部依赖数: {report['summary']['total_external_dependencies']}")
    print(f"循环依赖数: {report['summary']['circular_dependencies']}")
    print(f"依赖层次数: {report['summary']['dependency_layers']}")
    
    if report['circular_dependencies']:
        print("\n⚠️  发现循环依赖:")
        for i, cycle in enumerate(report['circular_dependencies'], 1):
            print(f"  {i}. {' -> '.join(cycle)}")
    
    print(f"\n📄 详细报告已保存到: dependency_analysis_report.json")
    print(f"📊 依赖关系图已保存到: dependency_diagram.mmd")


if __name__ == "__main__":
    main()
