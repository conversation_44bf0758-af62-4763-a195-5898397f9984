# NIFA Credit API Client 环境配置示例
# 复制此文件为 .env 并填入实际配置值

# ===== 基础配置 =====
# 运行环境 (development, testing, staging, production)
ENVIRONMENT=development

# 调试模式
DEBUG=true

# ===== NIFA API 配置 =====
# 机构代码（必填）
NIFA_ORG_CODE=your_organization_code

# API基础URL
NIFA_BASE_URL=https://api.nifa.org.cn

# 请求超时时间（秒）
NIFA_TIMEOUT=30

# 最大重试次数
NIFA_MAX_RETRIES=3

# 重试延迟时间（秒）
NIFA_RETRY_DELAY=1.0

# ===== 签名配置 =====
# 签名算法 (SHA256, MD5)
NIFA_SIGNATURE_ALGORITHM=SHA256

# ===== 加密配置 =====
# SM2公钥（十六进制字符串）
NIFA_SM2_PUBLIC_KEY=your_sm2_public_key

# SM2私钥（十六进制字符串）
NIFA_SM2_PRIVATE_KEY=your_sm2_private_key

# SM4密钥（32位十六进制字符串）
NIFA_SM4_KEY=your_32_char_hex_sm4_key

# ===== 日志配置 =====
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志格式
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ===== 缓存配置 =====
# 是否启用缓存
CACHE_ENABLED=false

# 缓存过期时间（秒）
CACHE_TTL=300

# ===== 连接池配置 =====
# 连接池大小
CONNECTION_POOL_SIZE=10

# 连接池最大大小
CONNECTION_POOL_MAX_SIZE=20
