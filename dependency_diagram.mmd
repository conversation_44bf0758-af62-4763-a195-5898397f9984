graph TD
    nifa_api_info["info"]
    nifa_api["api"]
    nifa_utils["utils"]
    nifa_exceptions["exceptions"]
    nifa_api_judicial["judicial"]
    nifa_models["models"]
    nifa_auth_signature["signature"]
    nifa_exceptions_base["base"]
    nifa_config_enhanced_settings["enhanced_settings"]
    nifa_core_circuit_breaker["circuit_breaker"]
    nifa_auth_encryption["encryption"]
    nifa_api_query_count["query_count"]
    nifa_utils_helpers["helpers"]
    nifa_api_task["task"]
    nifa_config_settings["settings"]
    nifa_api_data["data"]
    nifa_api_client["client"]
    nifa_config["config"]
    nifa_utils_validators["validators"]
    nifa_core["core"]
    nifa["nifa"]
    nifa_models_base["base"]
    nifa_auth["auth"]
    nifa --> nifa_api_info
    nifa --> nifa_exceptions_base
    nifa --> nifa_api_task
    nifa --> nifa_config_settings
    nifa --> nifa_api_data
    nifa --> nifa_api_judicial
    nifa --> nifa_api_query_count
    nifa_api_client --> nifa_exceptions_base
    nifa_api_client --> nifa_config_settings
    nifa_api_client --> nifa_auth_signature
    nifa_api_client --> nifa_utils_helpers
    nifa_api_data --> nifa_exceptions_base
    nifa_api_data --> nifa_config_settings
    nifa_api_data --> nifa_auth_encryption
    nifa_api_data --> nifa_utils_validators
    nifa_api_info --> nifa_exceptions_base
    nifa_api_info --> nifa_utils_validators
    nifa_api_judicial --> nifa_exceptions_base
    nifa_api_judicial --> nifa_utils_validators
    nifa_api_query_count --> nifa_exceptions_base
    nifa_api_query_count --> nifa_utils_validators
    nifa_api_task --> nifa_exceptions_base
    nifa_api_task --> nifa_utils_validators
    nifa_auth_encryption --> nifa_exceptions_base
    nifa_auth_signature --> nifa_exceptions_base
    nifa_auth_signature --> nifa_config_settings
    nifa_auth_signature --> nifa_utils_helpers
    nifa_utils_helpers --> nifa_exceptions_base
    nifa_utils_validators --> nifa_exceptions_base