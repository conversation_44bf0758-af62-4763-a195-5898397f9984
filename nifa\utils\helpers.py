"""
通用工具函数模块
包含各种辅助函数
"""

import time
import json
import hashlib
import logging
from datetime import datetime
from typing import Any, Dict, Optional, Callable, TypeVar, Union
from functools import wraps

from ..exceptions.base import NifaError, NifaTimeoutError, NifaNetworkError

logger = logging.getLogger(__name__)

T = TypeVar('T')


def format_datetime(dt: Optional[datetime] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化日期时间
    
    Args:
        dt: 日期时间对象，默认为当前时间
        format_str: 格式字符串
        
    Returns:
        格式化后的日期时间字符串
    """
    if dt is None:
        dt = datetime.now()
    return dt.strftime(format_str)


def parse_response(response_text: str) -> Dict[str, Any]:
    """
    解析响应文本
    
    Args:
        response_text: 响应文本
        
    Returns:
        解析后的字典
        
    Raises:
        NifaError: 解析失败时抛出
    """
    try:
        return json.loads(response_text)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse response: {response_text}")
        raise NifaError(f"响应解析失败: {str(e)}") from e


def generate_request_id() -> str:
    """
    生成请求ID
    
    Returns:
        请求ID
    """
    timestamp = str(int(time.time() * 1000))
    hash_obj = hashlib.md5(timestamp.encode())
    return hash_obj.hexdigest()[:16]


def calculate_md5(data: Union[str, bytes]) -> str:
    """
    计算MD5哈希值
    
    Args:
        data: 要计算哈希的数据
        
    Returns:
        MD5哈希值
    """
    if isinstance(data, str):
        data = data.encode('utf-8')
    return hashlib.md5(data).hexdigest()


def calculate_sha256(data: Union[str, bytes]) -> str:
    """
    计算SHA256哈希值
    
    Args:
        data: 要计算哈希的数据
        
    Returns:
        SHA256哈希值
    """
    if isinstance(data, str):
        data = data.encode('utf-8')
    return hashlib.sha256(data).hexdigest()


def retry_on_failure(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
) -> Callable:
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 退避因子
        exceptions: 需要重试的异常类型
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(
                            f"Function {func.__name__} failed after {max_retries} retries. "
                            f"Last error: {str(e)}"
                        )
                        break
                    
                    logger.warning(
                        f"Function {func.__name__} failed on attempt {attempt + 1}/{max_retries + 1}. "
                        f"Retrying in {current_delay} seconds. Error: {str(e)}"
                    )
                    
                    time.sleep(current_delay)
                    current_delay *= backoff_factor
            
            # 如果所有重试都失败了，抛出最后一个异常
            if isinstance(last_exception, (NifaTimeoutError, NifaNetworkError)):
                raise last_exception
            else:
                raise NifaNetworkError(
                    f"Function {func.__name__} failed after {max_retries} retries",
                    original_error=last_exception
                )
        
        return wrapper
    return decorator


def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    安全获取字典值
    
    Args:
        data: 数据字典
        key: 键名，支持点号分隔的嵌套键
        default: 默认值
        
    Returns:
        获取到的值或默认值
    """
    try:
        keys = key.split('.')
        result = data
        
        for k in keys:
            if isinstance(result, dict) and k in result:
                result = result[k]
            else:
                return default
        
        return result
    except (KeyError, TypeError, AttributeError):
        return default


def mask_sensitive_data(data: str, mask_char: str = "*", keep_start: int = 3, keep_end: int = 3) -> str:
    """
    掩码敏感数据
    
    Args:
        data: 原始数据
        mask_char: 掩码字符
        keep_start: 保留开头字符数
        keep_end: 保留结尾字符数
        
    Returns:
        掩码后的数据
    """
    if not data or len(data) <= keep_start + keep_end:
        return mask_char * len(data) if data else ""
    
    start = data[:keep_start]
    end = data[-keep_end:] if keep_end > 0 else ""
    middle = mask_char * (len(data) - keep_start - keep_end)
    
    return start + middle + end


def validate_response_structure(response: Dict[str, Any], required_fields: list) -> bool:
    """
    验证响应结构
    
    Args:
        response: 响应数据
        required_fields: 必需字段列表
        
    Returns:
        是否有效
        
    Raises:
        NifaError: 验证失败时抛出
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in response:
            missing_fields.append(field)
    
    if missing_fields:
        raise NifaError(f"响应缺少必需字段: {', '.join(missing_fields)}")
    
    return True


def log_request_response(
    method: str,
    url: str,
    request_data: Optional[Dict[str, Any]] = None,
    response_data: Optional[Dict[str, Any]] = None,
    duration: Optional[float] = None,
    mask_fields: Optional[list] = None
) -> None:
    """
    记录请求和响应日志
    
    Args:
        method: HTTP方法
        url: 请求URL
        request_data: 请求数据
        response_data: 响应数据
        duration: 请求耗时
        mask_fields: 需要掩码的字段
    """
    mask_fields = mask_fields or ['password', 'token', 'key', 'secret']
    
    # 掩码敏感字段
    def mask_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        if not isinstance(data, dict):
            return data
        
        masked_data = {}
        for key, value in data.items():
            if any(field in key.lower() for field in mask_fields):
                masked_data[key] = mask_sensitive_data(str(value))
            elif isinstance(value, dict):
                masked_data[key] = mask_dict(value)
            else:
                masked_data[key] = value
        
        return masked_data
    
    log_info = {
        "method": method,
        "url": url,
        "request_data": mask_dict(request_data) if request_data else None,
        "response_data": mask_dict(response_data) if response_data else None,
        "duration": f"{duration:.3f}s" if duration else None
    }
    
    logger.info(f"API Request/Response: {json.dumps(log_info, ensure_ascii=False, indent=2)}")


def convert_to_snake_case(camel_str: str) -> str:
    """
    将驼峰命名转换为下划线命名
    
    Args:
        camel_str: 驼峰命名字符串
        
    Returns:
        下划线命名字符串
    """
    import re
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def convert_to_camel_case(snake_str: str) -> str:
    """
    将下划线命名转换为驼峰命名
    
    Args:
        snake_str: 下划线命名字符串
        
    Returns:
        驼峰命名字符串
    """
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])
