"""
辅助工具函数测试
测试各种辅助工具函数
"""

import pytest
import json
import time
from unittest.mock import Mock, patch

from nifa.utils.helpers import (
    parse_response,
    log_request_response,
    validate_response_structure,
    retry_on_failure,
    mask_sensitive_data,
    format_datetime,
    calculate_md5,
    calculate_sha256,
    safe_get,
    generate_request_id,
    convert_to_snake_case,
    convert_to_camel_case
)
from nifa.exceptions.base import NifaResponseError, NifaAPIError, NifaError


class TestParseResponse:
    """响应解析测试"""

    def test_parse_valid_json_string(self):
        """测试解析有效JSON字符串"""
        json_string = '{"code": "0000", "message": "成功", "data": {"result": "test"}}'

        result = parse_response(json_string)
        assert result["code"] == "0000"
        assert result["data"]["result"] == "test"

    def test_parse_invalid_json_string(self):
        """测试解析无效JSON字符串"""
        invalid_json = '{"invalid": json}'

        with pytest.raises(NifaError):
            parse_response(invalid_json)

    def test_parse_empty_string(self):
        """测试解析空字符串"""
        with pytest.raises(NifaError):
            parse_response("")

    def test_parse_complex_json(self):
        """测试解析复杂JSON"""
        complex_json = '''
        {
            "code": "0000",
            "data": {
                "list": [{"id": 1}, {"id": 2}],
                "total": 2,
                "nested": {
                    "deep": {
                        "value": "test"
                    }
                }
            }
        }
        '''

        result = parse_response(complex_json)
        assert result["code"] == "0000"
        assert len(result["data"]["list"]) == 2
        assert result["data"]["nested"]["deep"]["value"] == "test"


class TestLogRequestResponse:
    """请求响应日志测试"""
    
    @patch('nifa.utils.helpers.logger')
    def test_log_request_response_success(self, mock_logger):
        """测试记录成功请求响应日志"""
        request_data = {"param": "value"}
        response_data = {"code": "0000", "data": {}}
        
        log_request_response(
            method="POST",
            url="https://api.test.com/endpoint",
            request_data=request_data,
            response_data=response_data,
            duration=0.5
        )
        
        # 验证日志被调用
        mock_logger.info.assert_called()
        call_args = mock_logger.info.call_args[0][0]
        assert "POST" in call_args
        assert "https://api.test.com/endpoint" in call_args
        assert "0.5" in call_args
    
    @patch('nifa.utils.helpers.logger')
    def test_log_request_response_with_sensitive_data(self, mock_logger):
        """测试记录包含敏感数据的请求响应"""
        request_data = {
            "password": "secret123",
            "idCard": "110101199001011237",
            "normal": "value"
        }
        response_data = {"code": "0000"}
        
        log_request_response(
            method="POST",
            url="https://api.test.com/endpoint",
            request_data=request_data,
            response_data=response_data,
            duration=0.1
        )
        
        # 验证敏感数据被掩码
        mock_logger.info.assert_called()


class TestValidateResponseStructure:
    """响应结构验证测试"""

    def test_validate_valid_structure(self):
        """测试验证有效响应结构"""
        response_data = {
            "code": "0000",
            "message": "成功",
            "data": {"result": "test"}
        }
        required_fields = ["code", "message"]

        # 应该不抛出异常
        result = validate_response_structure(response_data, required_fields)
        assert result is True

    def test_validate_minimal_structure(self):
        """测试验证最小响应结构"""
        response_data = {
            "code": "0000"
        }
        required_fields = ["code"]

        # 应该不抛出异常
        result = validate_response_structure(response_data, required_fields)
        assert result is True

    def test_validate_missing_fields(self):
        """测试验证缺少字段的结构"""
        response_data = {
            "code": "0000"
        }
        required_fields = ["code", "message", "data"]

        with pytest.raises(NifaError) as exc_info:
            validate_response_structure(response_data, required_fields)
        assert "响应缺少必需字段" in str(exc_info.value)
        assert "message" in str(exc_info.value)
        assert "data" in str(exc_info.value)

    def test_validate_empty_required_fields(self):
        """测试空必需字段列表"""
        response_data = {
            "any": "data"
        }
        required_fields = []

        result = validate_response_structure(response_data, required_fields)
        assert result is True


class TestRetryOnFailure:
    """失败重试测试"""
    
    def test_retry_success_on_first_attempt(self):
        """测试第一次尝试就成功"""
        @retry_on_failure(max_retries=3, delay=0.1)
        def successful_function():
            return "success"
        
        result = successful_function()
        assert result == "success"
    
    def test_retry_success_after_failures(self):
        """测试失败后重试成功"""
        call_count = 0
        
        @retry_on_failure(max_retries=3, delay=0.1)
        def function_with_retries():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = function_with_retries()
        assert result == "success"
        assert call_count == 3
    
    def test_retry_max_attempts_exceeded(self):
        """测试超过最大重试次数"""
        @retry_on_failure(max_retries=2, delay=0.1)
        def always_failing_function():
            raise Exception("Always fails")
        
        with pytest.raises(Exception):
            always_failing_function()
    
    def test_retry_with_specific_exceptions(self):
        """测试只对特定异常重试"""
        @retry_on_failure(max_retries=2, delay=0.1, exceptions=(ValueError,))
        def function_with_type_error():
            raise TypeError("Type error")
        
        # TypeError不在重试异常列表中，应该立即抛出
        with pytest.raises(TypeError):
            function_with_type_error()


class TestMaskSensitiveData:
    """敏感数据掩码测试"""

    def test_mask_default_settings(self):
        """测试默认掩码设置"""
        data = "1234567890"
        masked = mask_sensitive_data(data)

        assert masked == "123****890"

    def test_mask_short_data(self):
        """测试短数据掩码"""
        data = "123"
        masked = mask_sensitive_data(data)

        assert masked == "***"  # 短于保留长度时全部掩码

    def test_mask_custom_char(self):
        """测试自定义掩码字符"""
        data = "1234567890"
        masked = mask_sensitive_data(data, mask_char="X")

        assert masked == "123XXXX890"

    def test_mask_custom_keep_length(self):
        """测试自定义保留长度"""
        data = "1234567890"
        masked = mask_sensitive_data(data, keep_start=2, keep_end=2)

        assert masked == "12******90"

    def test_mask_no_end_keep(self):
        """测试不保留结尾"""
        data = "1234567890"
        masked = mask_sensitive_data(data, keep_end=0)

        assert masked == "123*******"

    def test_mask_empty_string(self):
        """测试空字符串掩码"""
        data = ""
        masked = mask_sensitive_data(data)

        assert masked == ""

    def test_mask_id_card_format(self):
        """测试身份证号掩码格式"""
        id_card = "110101199001011237"
        masked = mask_sensitive_data(id_card, keep_start=6, keep_end=4)

        assert masked == "110101********1237"

    def test_mask_phone_format(self):
        """测试手机号掩码格式"""
        phone = "13812345678"
        masked = mask_sensitive_data(phone, keep_start=3, keep_end=4)

        assert masked == "138****5678"


class TestFormatDatetime:
    """日期时间格式化测试"""
    
    def test_format_datetime_default(self):
        """测试默认格式化"""
        from datetime import datetime
        dt = datetime(2023, 12, 25, 10, 30, 45)
        
        formatted = format_datetime(dt)
        assert formatted == "2023-12-25 10:30:45"
    
    def test_format_datetime_custom_format(self):
        """测试自定义格式化"""
        from datetime import datetime
        dt = datetime(2023, 12, 25, 10, 30, 45)
        
        formatted = format_datetime(dt, "%Y/%m/%d")
        assert formatted == "2023/12/25"
    
    def test_format_datetime_none(self):
        """测试None值格式化"""
        formatted = format_datetime(None)
        # None值时使用当前时间，所以不会是空字符串
        assert len(formatted) > 0
        assert formatted.count('-') == 2  # 包含日期分隔符
        assert formatted.count(':') == 2  # 包含时间分隔符


class TestCalculateHash:
    """哈希计算测试"""

    def test_calculate_md5_string(self):
        """测试字符串MD5计算"""
        test_string = "hello world"
        hash_value = calculate_md5(test_string)

        assert len(hash_value) == 32
        assert hash_value.isalnum()
        assert hash_value == "5eb63bbbe01eeed093cb22bb8f5acdc3"

    def test_calculate_md5_bytes(self):
        """测试字节MD5计算"""
        test_bytes = b"hello world"
        hash_value = calculate_md5(test_bytes)

        assert len(hash_value) == 32
        assert hash_value == "5eb63bbbe01eeed093cb22bb8f5acdc3"

    def test_calculate_sha256_string(self):
        """测试字符串SHA256计算"""
        test_string = "hello world"
        hash_value = calculate_sha256(test_string)

        assert len(hash_value) == 64
        assert hash_value.isalnum()

    def test_calculate_sha256_bytes(self):
        """测试字节SHA256计算"""
        test_bytes = b"hello world"
        hash_value = calculate_sha256(test_bytes)

        assert len(hash_value) == 64


class TestSafeGet:
    """安全获取字典值测试"""

    def test_safe_get_existing_key(self):
        """测试获取存在的键"""
        data = {"key": "value", "nested": {"inner": "data"}}
        result = safe_get(data, "key")

        assert result == "value"

    def test_safe_get_nonexistent_key(self):
        """测试获取不存在的键"""
        data = {"key": "value"}
        result = safe_get(data, "nonexistent")

        assert result is None

    def test_safe_get_with_default(self):
        """测试带默认值的获取"""
        data = {"key": "value"}
        result = safe_get(data, "nonexistent", default="default_value")

        assert result == "default_value"

    def test_safe_get_nested_key(self):
        """测试获取嵌套键"""
        data = {"nested": {"inner": "data"}}
        result = safe_get(data, "nested.inner")

        assert result == "data"


class TestGenerateRequestId:
    """请求ID生成测试"""

    def test_generate_request_id_format(self):
        """测试请求ID格式"""
        request_id = generate_request_id()

        assert len(request_id) == 16
        assert request_id.isalnum()

    def test_generate_request_id_uniqueness(self):
        """测试请求ID唯一性"""
        id1 = generate_request_id()
        time.sleep(0.001)  # 确保时间戳不同
        id2 = generate_request_id()

        assert id1 != id2


class TestCaseConversion:
    """命名转换测试"""

    def test_convert_to_snake_case(self):
        """测试转换为下划线命名"""
        test_cases = [
            ("camelCase", "camel_case"),
            ("PascalCase", "pascal_case"),
            ("simpleWord", "simple_word"),
            ("XMLHttpRequest", "xml_http_request")
        ]

        for camel, expected_snake in test_cases:
            result = convert_to_snake_case(camel)
            assert result == expected_snake

    def test_convert_to_camel_case(self):
        """测试转换为驼峰命名"""
        test_cases = [
            ("snake_case", "snakeCase"),
            ("simple_word", "simpleWord"),
            ("multiple_words_here", "multipleWordsHere")
        ]

        for snake, expected_camel in test_cases:
            result = convert_to_camel_case(snake)
            assert result == expected_camel
