"""
熔断器模块
实现服务熔断和降级功能
"""

import time
import threading
from enum import Enum
from typing import Callable, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 关闭状态，正常工作
    OPEN = "open"          # 开启状态，熔断中
    HALF_OPEN = "half_open"  # 半开状态，尝试恢复


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5          # 失败阈值
    recovery_timeout: float = 60.0      # 恢复超时时间（秒）
    expected_exception: tuple = (Exception,)  # 预期的异常类型
    success_threshold: int = 3          # 半开状态下成功阈值
    timeout: float = 30.0               # 调用超时时间


class CircuitBreakerError(Exception):
    """熔断器异常"""
    pass


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.lock = threading.RLock()
        
    def __call__(self, func: Callable) -> Callable:
        """装饰器模式使用"""
        def wrapper(*args, **kwargs):
            return self.call(func, *args, **kwargs)
        return wrapper
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """执行被保护的函数"""
        with self.lock:
            # 检查熔断器状态
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info("熔断器进入半开状态")
                else:
                    raise CircuitBreakerError("熔断器处于开启状态，拒绝请求")
            
            try:
                # 执行函数
                start_time = time.time()
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 检查超时
                if duration > self.config.timeout:
                    raise TimeoutError(f"函数执行超时: {duration:.2f}s")
                
                # 成功处理
                self._on_success()
                return result
                
            except self.config.expected_exception as e:
                # 失败处理
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        return (time.time() - self.last_failure_time) >= self.config.recovery_timeout
    
    def _on_success(self) -> None:
        """成功回调"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                logger.info("熔断器恢复到关闭状态")
        elif self.state == CircuitState.CLOSED:
            self.failure_count = 0
    
    def _on_failure(self) -> None:
        """失败回调"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            logger.warning("熔断器从半开状态回到开启状态")
        elif (self.state == CircuitState.CLOSED and 
              self.failure_count >= self.config.failure_threshold):
            self.state = CircuitState.OPEN
            logger.warning(f"熔断器开启，失败次数: {self.failure_count}")
    
    def get_state(self) -> CircuitState:
        """获取当前状态"""
        return self.state
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time
        }
    
    def reset(self) -> None:
        """手动重置熔断器"""
        with self.lock:
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            self.success_count = 0
            self.last_failure_time = 0
            logger.info("熔断器已手动重置")


class CircuitBreakerManager:
    """熔断器管理器"""
    
    def __init__(self):
        self._breakers = {}
        self._lock = threading.RLock()
    
    def get_breaker(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """获取或创建熔断器"""
        with self._lock:
            if name not in self._breakers:
                if config is None:
                    config = CircuitBreakerConfig()
                self._breakers[name] = CircuitBreaker(config)
            return self._breakers[name]
    
    def remove_breaker(self, name: str) -> None:
        """移除熔断器"""
        with self._lock:
            self._breakers.pop(name, None)
    
    def get_all_stats(self) -> dict:
        """获取所有熔断器统计信息"""
        with self._lock:
            return {name: breaker.get_stats() for name, breaker in self._breakers.items()}
    
    def reset_all(self) -> None:
        """重置所有熔断器"""
        with self._lock:
            for breaker in self._breakers.values():
                breaker.reset()


# 全局熔断器管理器实例
circuit_breaker_manager = CircuitBreakerManager()