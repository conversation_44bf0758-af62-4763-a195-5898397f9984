"""
信用信息查询API
实现个人和企业信用信息查询功能
"""

import logging
from typing import Dict, Any, Optional

from .client import APIClient
from ..utils.validators import validate_id_card, validate_name, validate_required_fields
from ..exceptions.base import NifaValidationError

logger = logging.getLogger(__name__)


class InfoAPI:
    """信用信息查询API类"""
    
    def __init__(self, client: Optional[APIClient] = None):
        """
        初始化信用信息查询API
        
        Args:
            client: API客户端实例，如果不提供则创建新实例
        """
        self.client = client or APIClient()
        self._own_client = client is None
    
    def query_personal_credit(
        self,
        id_card: str,
        name: str,
        query_type: str = "01",
        query_reason: str = "信用查询",
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询个人信用信息
        
        Args:
            id_card: 身份证号码
            name: 姓名
            query_type: 查询类型（01-基础信息，02-详细信息）
            query_reason: 查询原因
            **kwargs: 其他参数
            
        Returns:
            查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        validate_id_card(id_card)
        validate_name(name)
        
        if query_type not in ["01", "02"]:
            raise NifaValidationError(
                "查询类型必须为01（基础信息）或02（详细信息）",
                field="query_type",
                value=query_type
            )
        
        # 构建请求数据
        request_data = {
            "idCard": id_card,
            "name": name,
            "queryType": query_type,
            "queryReason": query_reason,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["idCard", "name", "queryType"])
        
        logger.info(f"查询个人信用信息: {name}({id_card[:6]}***{id_card[-4:]})")
        
        # 发起请求
        response = self.client.post("personal/credit/query", data=request_data)
        
        return response
    
    def query_enterprise_credit(
        self,
        org_code: str,
        org_name: str,
        query_type: str = "01",
        query_reason: str = "信用查询",
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询企业信用信息
        
        Args:
            org_code: 企业统一社会信用代码或组织机构代码
            org_name: 企业名称
            query_type: 查询类型（01-基础信息，02-详细信息）
            query_reason: 查询原因
            **kwargs: 其他参数
            
        Returns:
            查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not org_code or not org_code.strip():
            raise NifaValidationError("企业代码不能为空", field="org_code", value=org_code)
        
        if not org_name or not org_name.strip():
            raise NifaValidationError("企业名称不能为空", field="org_name", value=org_name)
        
        if query_type not in ["01", "02"]:
            raise NifaValidationError(
                "查询类型必须为01（基础信息）或02（详细信息）",
                field="query_type",
                value=query_type
            )
        
        # 构建请求数据
        request_data = {
            "orgCode": org_code,
            "orgName": org_name,
            "queryType": query_type,
            "queryReason": query_reason,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["orgCode", "orgName", "queryType"])
        
        logger.info(f"查询企业信用信息: {org_name}({org_code})")
        
        # 发起请求
        response = self.client.post("enterprise/credit/query", data=request_data)
        
        return response
    
    def query_credit_report(
        self,
        report_id: str,
        report_type: str = "personal",
        **kwargs
    ) -> Dict[str, Any]:
        """
        查询信用报告
        
        Args:
            report_id: 报告ID
            report_type: 报告类型（personal-个人，enterprise-企业）
            **kwargs: 其他参数
            
        Returns:
            查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not report_id or not report_id.strip():
            raise NifaValidationError("报告ID不能为空", field="report_id", value=report_id)
        
        if report_type not in ["personal", "enterprise"]:
            raise NifaValidationError(
                "报告类型必须为personal（个人）或enterprise（企业）",
                field="report_type",
                value=report_type
            )
        
        # 构建请求数据
        request_data = {
            "reportId": report_id,
            "reportType": report_type,
            **kwargs
        }
        
        # 验证必填字段
        validate_required_fields(request_data, ["reportId", "reportType"])
        
        logger.info(f"查询信用报告: {report_id} ({report_type})")
        
        # 发起请求
        response = self.client.post("credit/report/query", data=request_data)
        
        return response
    
    def batch_query_personal_credit(
        self,
        query_list: list,
        query_type: str = "01",
        query_reason: str = "批量信用查询",
        **kwargs
    ) -> Dict[str, Any]:
        """
        批量查询个人信用信息
        
        Args:
            query_list: 查询列表，每个元素包含idCard和name字段
            query_type: 查询类型（01-基础信息，02-详细信息）
            query_reason: 查询原因
            **kwargs: 其他参数
            
        Returns:
            查询结果
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        if not query_list or not isinstance(query_list, list):
            raise NifaValidationError("查询列表不能为空且必须为列表", field="query_list", value=query_list)
        
        if len(query_list) > 100:  # 假设批量查询限制为100条
            raise NifaValidationError(
                "批量查询数量不能超过100条",
                field="query_list",
                value=len(query_list)
            )
        
        # 验证每个查询项
        for i, item in enumerate(query_list):
            if not isinstance(item, dict):
                raise NifaValidationError(
                    f"查询列表第{i+1}项必须为字典",
                    field=f"query_list[{i}]",
                    value=item
                )
            
            validate_required_fields(item, ["idCard", "name"])
            validate_id_card(item["idCard"])
            validate_name(item["name"])
        
        if query_type not in ["01", "02"]:
            raise NifaValidationError(
                "查询类型必须为01（基础信息）或02（详细信息）",
                field="query_type",
                value=query_type
            )
        
        # 构建请求数据
        request_data = {
            "queryList": query_list,
            "queryType": query_type,
            "queryReason": query_reason,
            **kwargs
        }
        
        logger.info(f"批量查询个人信用信息: {len(query_list)}条记录")
        
        # 发起请求
        response = self.client.post("personal/credit/batch-query", data=request_data)
        
        return response
    
    def get_query_history(
        self,
        start_date: str,
        end_date: str,
        query_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
        **kwargs
    ) -> Dict[str, Any]:
        """
        获取查询历史记录
        
        Args:
            start_date: 开始日期（YYYY-MM-DD）
            end_date: 结束日期（YYYY-MM-DD）
            query_type: 查询类型（可选）
            page: 页码
            page_size: 每页大小
            **kwargs: 其他参数
            
        Returns:
            查询历史记录
            
        Raises:
            NifaValidationError: 参数验证失败时抛出
        """
        # 参数验证
        from ..utils.validators import validate_date_string
        validate_date_string(start_date)
        validate_date_string(end_date)
        
        if page < 1:
            raise NifaValidationError("页码必须大于0", field="page", value=page)
        
        if page_size < 1 or page_size > 100:
            raise NifaValidationError(
                "每页大小必须在1-100之间",
                field="page_size",
                value=page_size
            )
        
        # 构建请求数据
        request_data = {
            "startDate": start_date,
            "endDate": end_date,
            "page": page,
            "pageSize": page_size,
            **kwargs
        }
        
        if query_type:
            request_data["queryType"] = query_type
        
        logger.info(f"获取查询历史记录: {start_date} 至 {end_date}")
        
        # 发起请求
        response = self.client.post("query/history", data=request_data)
        
        return response
    
    def close(self) -> None:
        """关闭API客户端"""
        if self._own_client and self.client:
            self.client.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
